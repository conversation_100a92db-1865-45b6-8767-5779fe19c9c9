import os
import json
from pathlib import Path
from typing import Dict, Any, List, Optional

import pandas as pd

from helper.utils import (
    fetch_data_total,
    generate_dgr_wind_report,
    get_dynamic_dates,
    generate_combined_wind_pdf,
    get_capacity_from_csv,
    get_turbine_metadata_from_csv,
    _safe_mean,
    _safe_sum
)
from helper.logger_setup import setup_logger
from helper.wind_plots import merge_pdfs_plots
from helper.storage_s3 import upload_file_s3
from DB.db_ops import insert_wind_data_db, get_wind_report_data

# -------------------------------
# Constants
# -------------------------------
STATIC_REPORT_DIR = Path("static") / "wind_final_report"
S3_REPORT_PREFIX = "wind_reports/"
LOGGER = setup_logger("wind_automation", "wind_automation.log")


# -------------------------------
# Data Fetching
# -------------------------------
def fetch_all_data_wind(
    plant_name: str,
    start_date: str,
    current_month_start: str,
    last_30_days_start: str,
    current_year_start: str,
    yearly_date: str,
    condition_wind: Dict[str, str],
    condition_generation: Dict[str, str]
) -> Dict[str, pd.DataFrame]:
    """
    Fetch all required wind plant datasets sequentially to reduce memory usage.
    """
    LOGGER.info(f"[Data Fetch] Plant: {plant_name}, Start Date: {start_date}")

    return {
        "wind_speed_data_value": fetch_data_total(
            plant_name, ["WTUR.Wind-Speed"], "Turbine", start_date, start_date, condition_wind
        ),
        "daily_generation_value": fetch_data_total(
            plant_name, ["WTUR.Generation today"], "Turbine", start_date, start_date, condition_generation
        ),
        "wind_data_monthly_value": fetch_data_total(
            plant_name, ["WTUR.Wind-Speed"], "Turbine", current_month_start, start_date, condition_wind
        ),
        "generation_monthly_value": fetch_data_total(
            plant_name, ["WTUR.Generation today"], "Turbine", current_month_start, start_date, condition_generation
        ),
    }


# -------------------------------
# CSV & Metadata Handling
# -------------------------------
def merge_csv_with_metadata(csv_path: Path, plant_name: str) -> Optional[str]:
    """
    Merge CSV report data with turbine metadata and return as JSON string.
    """
    if not csv_path.exists():
        LOGGER.warning(f"No CSV report found at {csv_path}")
        return None

    try:
        csv_report_list = pd.read_csv(csv_path).to_dict(orient="records")

        turbine_metadata_str = get_turbine_metadata_from_csv(plant_name)
        turbine_metadata_list = _safe_parse_metadata(turbine_metadata_str)

        # Index data by 'Loc No' for quick merging
        meta_by_loc = {str(d.get("Loc No")).strip(): d for d in turbine_metadata_list}
        csv_by_loc = {str(d.get("Loc No")).strip(): d for d in csv_report_list}

        # Collect all keys from metadata
        all_keys = {k for d in turbine_metadata_list for k in d}

        merged_list = []
        for loc_no, meta in meta_by_loc.items():
            report_row = csv_by_loc.get(loc_no, {}).copy()
            for k in all_keys:
                if k not in report_row and k in meta:
                    report_row[k] = meta[k]
            report_row["Loc No"] = loc_no
            merged_list.append(report_row)

        # Include extra CSV rows not in metadata
        for loc_no, report_row in csv_by_loc.items():
            if loc_no not in meta_by_loc:
                merged_list.append(report_row)

        # Filter out entries where Loc No is 0 (as string or int)
        filtered_list = [row for row in merged_list if str(row.get("Loc No", "")).strip() not in ("0", 0)]

        return json.dumps(filtered_list)

    except Exception as e:
        LOGGER.error(f"Error merging CSV with metadata: {e}", exc_info=True)
        return None


def _safe_parse_metadata(raw_str: Optional[str]) -> List[Dict[str, Any]]:
    """
    Safely parse turbine metadata JSON from CSV helper.
    """
    if not raw_str or raw_str == "N/A":
        return []
    try:
        return json.loads(raw_str.replace('""', '"'))
    except Exception as e:
        LOGGER.error(f"Error parsing turbine metadata: {e}")
        return []


# -------------------------------
# Main Report Generation
# -------------------------------
def generate_wind_automation_report(
    plant_name: str,
    start_date: str,
    customer_name: str,
    project: str,
    ma_percent: float
) -> Optional[Path]:
    """
    Generate and upload the daily wind automation report for a given plant.
    """
    LOGGER.info(f"[Start Report] Plant: {plant_name}, Date: {start_date}")

    try:
        current_month_start, _, _, _, _, last_30_days_start, current_year_start, last_year_date = get_dynamic_dates(start_date)
        condition_wind = {"Wind-Speed": "mean"}
        condition_generation = {"Generation today": "last"}

        # Fetch data
        data = fetch_all_data_wind(
            plant_name, start_date, current_month_start,
            last_30_days_start, current_year_start, last_year_date,
            condition_wind, condition_generation
        )

        # Calculate KPIs
        avg_wind_speed = _safe_mean(data["wind_speed_data_value"])
        total_generation = _safe_sum(data["daily_generation_value"])
        monthly_wind = _safe_mean(data["wind_data_monthly_value"])
        monthly_generation = _safe_sum(data["generation_monthly_value"])

        # Generate CSV and PDF
        csv_report = generate_dgr_wind_report(
            plant_name, data["wind_speed_data_value"], data["daily_generation_value"],
            start_date, customer_name, project, ma_percent
        )

        capacity = get_capacity_from_csv(plant_name)
        STATIC_REPORT_DIR.mkdir(parents=True, exist_ok=True)
        final_pdf_path = STATIC_REPORT_DIR / f"{plant_name}_DGR_{start_date}.jpg"

        partial_pdf = generate_combined_wind_pdf(
            plant_name, start_date, customer_name, project,
            avg_wind_speed, total_generation, ma_percent,
            monthly_wind, monthly_generation, csv_report,
            capacity, final_pdf_path, comment_text=None
        )

        # Upload to S3
        s3_path = f"{S3_REPORT_PREFIX}{plant_name}_DGR_{start_date}.jpg"
        upload_file_s3(partial_pdf, s3_path)

        # Prepare DB record
        record = {
            "date": start_date,
            "plant_short_name": plant_name,
            "plant_long_name": customer_name,
            "generation": round(total_generation, 2),
            "wind_speed": round(avg_wind_speed, 2),
            "generation_monthly": round(monthly_generation, 2),
            "wind_speed_monthly": round(monthly_wind, 2),
            "approved": 0,
            "review": 0,
            "action_performed": 0,
            "dgr_path": str(partial_pdf),
            "csv_report_data": merge_csv_with_metadata(Path(csv_report), plant_name)
        }

        insert_wind_data_db([record])
        LOGGER.info(f"[Success] Report generated and stored for {plant_name}")

        # Cleanup
        if csv_report and Path(csv_report).exists():
            Path(csv_report).unlink()

        return partial_pdf

    except Exception as e:
        LOGGER.error(f"[Error] Report generation failed for {plant_name}: {e}", exc_info=True)
        _insert_zero_data(plant_name, start_date, customer_name)
        return None


# -------------------------------
# Helpers
# -------------------------------
def _insert_zero_data(plant_name: str, date: str, customer_name: str) -> None:
    """
    Insert a zeroed-out record into DB when report generation fails.
    """
    final_pdf_path = STATIC_REPORT_DIR / f"{plant_name}_DGR_{date}.jpg"
    # Fallback: parse and normalize turbine metadata for DB insertion
    turbine_metadata_str = get_turbine_metadata_from_csv(plant_name)
    turbine_metadata_list = _safe_parse_metadata(turbine_metadata_str)
    csv_report_data = json.dumps(turbine_metadata_list)

    record = {
        "date": date,
        "plant_short_name": plant_name,
        "plant_long_name": customer_name,
        "generation": 0,
        "wind_speed": 0,
        "generation_monthly": 0,
        "wind_speed_monthly": 0,
        "approved": 0,
        "review": 0,
        "action_performed": 0,
        "dgr_path": str(final_pdf_path),
        "csv_report_data": csv_report_data
    }
    try:
        insert_wind_data_db([record])
        LOGGER.info(f"[Fallback] Zero data inserted for {plant_name}")
    except Exception as e:
        LOGGER.error(f"[DB Error] Failed inserting zero data for {plant_name}: {e}", exc_info=True)
