<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Alarms Data Input</title>
  <style>
    .alarms-form-group {
      margin-bottom: 1rem;
    }
    .alarms-form-label {
      font-weight: 500;
      margin-bottom: 0.3rem;
      display: block;
    }
    .alarms-form-input, .alarms-form-select {
      width: 100%;
      padding: 0.5rem;
      border-radius: 0.375rem;
      border: 1px solid #ced4da;
      font-size: 1rem;
    }
    .alarms-form-btn {
      background: #dc3545;
      color: #fff;
      border: none;
      border-radius: 0.375rem;
      padding: 0.5rem 1.2rem;
      font-weight: 600;
      cursor: pointer;
      margin-right: 0.5rem;
    }
    .alarms-form-btn:disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }
  </style>
</head>
<body>
  <form id="alarmsDataForm">
    <div class="alarms-form-group">
      <label class="alarms-form-label" for="date">Date</label>
      <input class="alarms-form-input" type="date" id="date" name="date" required />
    </div>
    <div class="alarms-form-group">
      <label class="alarms-form-label" for="plantid">Plant ID</label>
      <input class="alarms-form-input" type="text" id="plantid" name="plantid" required />
    </div>
    <div class="alarms-form-group">
      <label class="alarms-form-label" for="plantname">Plant Name</label>
      <input class="alarms-form-input" type="text" id="plantname" name="plantname" required />
    </div>
    <div class="alarms-form-group">
      <label class="alarms-form-label" for="alarmname">Alarm Name</label>
      <input class="alarms-form-input" type="text" id="alarmname" name="alarmname" required />
    </div>
    <div class="alarms-form-group">
      <label class="alarms-form-label" for="controllername">Controller Name</label>
      <input class="alarms-form-input" type="text" id="controllername" name="controllername" required />
    </div>
    <div class="alarms-form-group">
      <label class="alarms-form-label" for="message">Message</label>
      <input class="alarms-form-input" type="text" id="message" name="message" required />
    </div>
    <div class="alarms-form-group">
      <label class="alarms-form-label" for="severity">Severity</label>
      <select class="alarms-form-select" id="severity" name="severity" required>
        <option value="">Select Severity</option>
        <option value="Critical">Critical</option>
        <option value="Major">Major</option>
        <option value="Minor">Minor</option>
        <option value="Warning">Warning</option>
        <option value="Info">Info</option>
      </select>
    </div>
    <div class="alarms-form-group">
      <label class="alarms-form-label" for="state">State</label>
      <select class="alarms-form-select" id="state" name="state" required>
        <option value="">Select State</option>
        <option value="Active">Active</option>
        <option value="Inactive">Inactive</option>
        <option value="Acknowledged">Acknowledged</option>
        <option value="Cleared">Cleared</option>
      </select>
    </div>
    <div style="text-align:right;">
      <button type="submit" class="alarms-form-btn">Submit</button>
      <button type="button" class="alarms-form-btn" style="background:#6c757d;" onclick="window.closeModal && window.closeModal()">Cancel</button>
    </div>
  </form>
</body>
</html>
