import os
from flask import request
from whatsapp.message_extraction import extract_whatsapp_message_info, update_or_create_whatsapp_status
from DB.db_ops import fetch_messages_by_message_id
from helper.logger_setup import setup_logger
from whatsapp.sender_whatsapp import send_msg
from datetime import datetime
from app.process_whatsapp import *


import re
import asyncio
from mcp_bot.client_runner import run_agent

logging = setup_logger('webhook', 'webhook.log')

VERIFY_TOKEN = os.getenv('VERIFY_TOKEN')

def verify_webhook():
    logging.info("Webhook initialized")
    mode = request.args.get('hub.mode')
    challenge = request.args.get('hub.challenge')
    token = request.args.get('hub.verify_token')

    if mode and token:
        if mode == "subscribe" and token == VERIFY_TOKEN:
            logging.info("Webhook initialized Successfully")
            return challenge, 200
        else:
            logging.info("Webhook Verification failed")
            return "Verification failed", 403






# ----------------------------
# Webhook handler
# ----------------------------


def handle_webhook():
    if request.method == 'POST':
        message_details = request.json
        logging.info(f"Received message: {message_details}")
        try:
            # incoming_num, incoming_message, button_text, message_id = extract_whatsapp_message_info(message_details)
            # mark_message_as_read(message_id)
            # Process status updates (sent, delivered, read)
            if "statuses" in message_details['entry'][0]['changes'][0]['value']:
                status_entry = message_details['entry'][0]['changes'][0]['value']['statuses'][0]

                message_id = status_entry.get('id')
                status = status_entry.get('status')
                timestamp = status_entry.get('timestamp')  # in Unix time
                recipient_id = status_entry.get('recipient_id')

                # Convert timestamp to datetime
                status_time = datetime.fromtimestamp(int(timestamp))

                # Update or insert in database
                update_or_create_whatsapp_status(message_id, recipient_id, status, status_time)

                return 'status update stored', 200

            # Process interactive replies (Yes/No)
            incoming_num, incoming_message, button_text, message_id = extract_whatsapp_message_info(message_details)
            if button_text and message_id:
                if button_text == 'Yes':
                    user_data = fetch_messages_by_message_id(message_id)
                    record = user_data[0]

                    report_type = record['report_type']
                    incoming_num = record['wa_id']
                    plant_short_name = record['plant_short_name']
                    plant_long_name = record['plant_long_name']
                    date = record['date']

                    if report_type == 'solar':
                        send_yes_solar(plant_short_name, plant_long_name, str(date), report_type, incoming_num)
                    elif report_type == 'wind':
                        send_yes_wind(plant_short_name, plant_long_name, str(date), report_type, incoming_num)
                    elif report_type == 'combined':
                        send_yes_combined(plant_short_name, plant_long_name, str(date), report_type, incoming_num)

                elif button_text == 'No':
                    send_msg(incoming_num, "Thank you for using our service.")

            # Process normal text messages
            elif incoming_message:
                local_number = re.sub(r"^91", "", incoming_num)
                # Run agent safely in sync context
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                response_bot = loop.run_until_complete(run_agent(local_number, incoming_message))
                loop.close()

                send_msg(incoming_num, str(response_bot))

        except Exception as e:
            logging.error(f"Webhook error: {e}")
        return 'success', 200
    else:
        return 'success', 200


    