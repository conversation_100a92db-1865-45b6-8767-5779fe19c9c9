from app.webhook import handle_webhook, verify_webhook
from app.frontend_handler import (
    index, get_both_reports, update_both, get_solar_reports,
    update_solar, get_dgr_reports, update_dgr_wind, serve_pdf,
    login, logout, api_regenerate, get_status_counts, edit_report,
    get_data_analysis, get_plant_date_options,
    get_report_status_options, get_report_status_data,
    get_report_status_unique_statuses, get_30day_trend,
    get_report_status_counts, update_csv_data, get_whatsapp_status,
    get_generation_diff, alarms_data_popup
)

def setup_routes(app):
    app.add_url_rule('/alarms_data_popup', 'alarms_data_popup', alarms_data_popup)
    app.add_url_rule('/webhook', 'handle_webhook', handle_webhook, methods=['POST'])
    app.add_url_rule('/webhook', 'verify_webhook', verify_webhook, methods=['GET'])
    app.add_url_rule('/api/both', 'get_both_reports', get_both_reports, methods=['GET'])
    app.add_url_rule('/api/both/update', 'update_both', update_both, methods=['POST'])
    app.add_url_rule('/api/solar', 'get_solar_reports', get_solar_reports, methods=['GET'])
    app.add_url_rule('/api/solar/update', 'update_solar', update_solar, methods=['POST'])
    app.add_url_rule('/api/wind', 'get_dgr_reports', get_dgr_reports, methods=['GET'])
    app.add_url_rule('/api/wind/update', 'update_dgr_wind', update_dgr_wind, methods=['POST'])
    app.add_url_rule('/', 'index', index)
    app.add_url_rule('/api/pdf/<report_type>/<int:report_id>', 'serve_pdf', serve_pdf)
    app.add_url_rule('/login', 'login', login, methods=['GET', 'POST'])
    app.add_url_rule('/logout', 'logout', logout)
    app.add_url_rule('/api/regenerate', 'api_regenerate', api_regenerate, methods=['POST'])
    app.add_url_rule('/api/status_counts', 'get_status_counts', get_status_counts, methods=['GET'])
    app.add_url_rule('/api/edit/<type>/<id>', 'edit_report', edit_report, methods=['POST'])
    app.add_url_rule('/api/data-analysis', 'get_data_analysis', get_data_analysis, methods=['GET'])
    app.add_url_rule('/api/30day-trend', 'get_30day_trend', get_30day_trend, methods=['GET'])
    app.add_url_rule('/api/plant-date-options', 'get_plant_date_options', get_plant_date_options, methods=['GET'])
    app.add_url_rule('/api/report-status-options', 'get_report_status_options', get_report_status_options, methods=['GET'])
    app.add_url_rule('/api/report-status-data', 'get_report_status_data', get_report_status_data, methods=['GET'])
    app.add_url_rule('/api/report-status-unique-statuses', 'get_report_status_unique_statuses', get_report_status_unique_statuses, methods=['GET'])
    app.add_url_rule('/api/report-status-counts', 'get_report_status_counts', get_report_status_counts, methods=['GET'])
    app.add_url_rule('/api/csv-data/<type>/<int:report_id>', 'update_csv_data', update_csv_data, methods=['POST'])
    app.add_url_rule('/api/whatsapp-status/<type>/<int:report_id>', 'get_whatsapp_status', get_whatsapp_status, methods=['GET'])
    app.add_url_rule('/api/generation-diff', 'get_generation_diff', get_generation_diff, methods=['GET'])
