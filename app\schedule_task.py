from app.process_tasks import process_plant_solar, process_plant_wind, process_plant_both
import schedule
import time
from helper.utils import get_data_wind_solar, get_both_plant_pairs_from_csv
from datetime import datetime, timedelta
from helper.logger_setup import setup_logger
from config.settings import Config


logging = setup_logger('schedule_tasks', 'schedule_tasks.log')

CSV_PATH = Config.CUSTOMER_DATA_CSV_PATH

def scheduled_task_solar(yesterday):
    """
    Schedules sequential processing of solar plants for the given date to reduce RAM usage.
    Reads plant and customer data from the provided CSV file.
    """
    logging.info(f"Scheduled solar tasks starting for {yesterday}.")

    plant_customer_pairs = get_data_wind_solar(CSV_PATH, 'solar')

    if not plant_customer_pairs:
        logging.warning("No solar plants found in the CSV.")
        return

    report_type = 'SOLAR INTEGRUM'

    # Process plants sequentially instead of concurrently
    for plant, customer in plant_customer_pairs:
        logging.info(f"Processing {plant} - {customer}")
        try:
            process_plant_solar(plant, customer, yesterday, report_type)
            logging.info(f"Successfully processed {plant} - {customer}")
        except Exception as e:
            logging.error(f"Error processing plant {plant}: {e}", exc_info=True)

    logging.info(f"Scheduled solar tasks executed for {yesterday}.")



def scheduled_task_wind(yesterday):
    """
    Schedules sequential processing of wind plants for the given date to reduce RAM usage.
    Reads plant and customer data from the provided CSV file.
    """
    logging.info(f"Scheduled wind tasks starting for {yesterday}.")

    plant_customer_pairs = get_data_wind_solar(CSV_PATH, 'wind')

    if not plant_customer_pairs:
        logging.warning("No wind plants found in the CSV.")
        return

    report_type = 'WIND INTEGRUM'

    # Process plants sequentially instead of concurrently
    for plant, customer in plant_customer_pairs:
        logging.info(f"Processing {plant} - {customer}")
        try:
            process_plant_wind(plant, customer, yesterday, report_type)
            logging.info(f"Successfully processed {plant} - {customer}")
        except Exception as e:
            logging.error(f"Error processing plant {plant}: {e}", exc_info=True)

    logging.info(f"Scheduled wind tasks executed for {yesterday}.")


def scheduled_task_both(yesterday):
    logging.info(f"Scheduled both-plants tasks starting for {yesterday}.")

    both_plant_pairs = get_both_plant_pairs_from_csv(CSV_PATH)

    if not both_plant_pairs:
        logging.warning("No both-plant pairs found in the CSV.")
        return

    report_type = 'INTEGRUM'

    # Process plants sequentially instead of concurrently
    for plant_solar, plant_wind, customer in both_plant_pairs:
        logging.info(f"Processing {plant_solar} & {plant_wind} - {customer}")
        try:
            process_plant_both(plant_solar, plant_wind, customer, yesterday, report_type)
            logging.info(f"Successfully processed {plant_solar} & {plant_wind} - {customer}")
        except Exception as e:
            logging.error(f"Error processing plants {plant_solar} & {plant_wind}: {e}", exc_info=True)

    logging.info(f"Scheduled both-plants tasks executed for {yesterday}.")

def get_yesterday():
    return (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")

import os
import time

def cleanup_old_files(folder_paths, days=3):
    """
    Remove files older than `days` in the given folder(s).

    :param folder_paths: list of folder paths
    :param days: number of days (default = 3)
    """
    # Convert days to seconds
    cutoff_time = time.time() - (days * 86400)

    for folder in folder_paths:
        if not os.path.exists(folder):
            print(f"⚠️ Folder does not exist: {folder}")
            continue

        for filename in os.listdir(folder):
            file_path = os.path.join(folder, filename)

            if os.path.isfile(file_path):
                file_mtime = os.path.getmtime(file_path)

                if file_mtime < cutoff_time:
                    try:
                        os.remove(file_path)
                        print(f"🗑️ Removed: {file_path}")
                    except Exception as e:
                        print(f"❌ Failed to remove {file_path}: {e}")

def run_scheduler():
    """
    Run the scheduler for processing all plant types.
    Can be run immediately or scheduled for daily execution.
    """
    logging.info("Scheduler started running.")


    # Get yesterday's date for processing
    yesterday = get_yesterday()
    # yesterday = '2025-01-15'  # Uncomment for testing with specific date

    # Process all plant types sequentially
    # scheduled_task_solar(yesterday)
    # scheduled_task_wind(yesterday)
    # scheduled_task_both(yesterday)


    #Uncomment these lines to enable daily scheduled execution at 1:00 AM
    schedule.every().day.at("01:00").do(lambda: scheduled_task_solar(get_yesterday()))
    schedule.every().day.at("01:00").do(lambda: scheduled_task_wind(get_yesterday()))
    schedule.every().day.at("01:00").do(lambda: scheduled_task_both(get_yesterday()))
    schedule.every().day.at("01:00").do(
    lambda: cleanup_old_files(
        ["export_plots", "/exports"],
        days=3
    )
)

    # Keep the scheduler running (uncomment if using scheduled execution)
    while True:
      schedule.run_pending()
      time.sleep(60)


