<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>DGR Validation Dashboard</title>
  <link rel="icon" href="{{ url_for('static', filename='logo_integrum.jpg') }}" type="image/jpeg">
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" />
  <!-- Font Awesome for Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" />
  <style>
    :root {
      --primary-color: #0d6efd;
      --secondary-color: #6c757d;
      --success-color: #198754;
      --warning-color: #ffc107;
      --danger-color: #dc3545;
      --info-color: #0dcaf0;
      --light-gray: #f8f9fa;
      --medium-gray: #e9ecef;
      --dark-gray: #343a40;
      --body-bg: #f4f7fc;
      --card-bg: #ffffff;
      --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      --border-radius: 0.5rem; /* 8px */
      --font-family-sans-serif: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    body {
      background-color: var(--body-bg);
      font-family: var(--font-family-sans-serif);
      color: var(--dark-gray);
    }

    .navbar {
      background-color: var(--card-bg);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      padding: 0.5rem 1.5rem; /* Reduced padding */
    }

    .navbar-brand img {
      height: 70px; /* Increased logo size */
      width: auto;
    }

    .main-content {
      padding-top: 2rem;
      padding-bottom: 3rem;
    }

    h1.page-title {
      font-weight: 600;
      color: var(--dark-gray);
      margin-bottom: 1.5rem;
      text-align: center;
    }

    .card {
      background-color: var(--card-bg);
      border: none;
      border-radius: var(--border-radius);
      box-shadow: var(--card-shadow);
      margin-bottom: 1.5rem;
    }

    .card-header {
      background-color: transparent;
      border-bottom: 1px solid var(--medium-gray);
      padding: 1rem 1.5rem;
      font-weight: 600;
      color: var(--primary-color);
    }

    .card-body {
      padding: 1.5rem;
    }

    .form-label {
      font-weight: 500;
      margin-bottom: 0.5rem;
      font-size: 0.9rem;
      color: var(--secondary-color);
    }

    .form-control, .btn {
      border-radius: 0.375rem; /* 6px */
    }

    .btn-primary {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
      transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
    }
    .btn-primary:hover {
      background-color: #0b5ed7;
      border-color: #0a58ca;
    }

    /* Status Count Boxes Styling */
    .status-card {
      border-left: 4px solid;
      padding: 0.75rem 1rem; /* Adjusted padding */
      display: flex;
      align-items: center;
      justify-content: space-between;
      background-color: var(--light-gray);
      border-radius: 0.375rem;
      transition: transform 0.2s ease;
    }
    .status-card:hover {
        transform: translateY(-2px);
    }
    .status-card .status-icon {
      font-size: 1.2rem; /* Adjusted icon size */
      margin-right: 0.75rem;
    }
    .status-card .status-text {
      font-weight: 500;
      font-size: 0.85rem; /* Adjusted text size */
      color: var(--secondary-color);
    }
    .status-card .status-count {
      font-size: 1.4rem; /* Adjusted count size */
      font-weight: 600;
    }
    /* Popup status card: more vibrant look */
    .status-card-popup {
      background: linear-gradient(90deg, #f8f9fa 70%, #e3f0ff 100%) !important;
      box-shadow: 0 2px 8px rgba(13,110,253,0.08);
      border-radius: 0.5rem;
      border-width: 2.5px !important;
    }
    .status-card.border-success { border-color: var(--success-color); }
    .status-card.border-secondary { border-color: var(--secondary-color); }
    .status-card.border-warning { border-color: var(--warning-color); }
    .status-card.border-info { border-color: var(--info-color); }
    .status-card.border-danger { border-color: var(--danger-color); }
    .status-card .text-success { color: var(--success-color) !important; }
    .status-card .text-secondary { color: var(--secondary-color) !important; }
    .status-card .text-warning { color: var(--warning-color) !important; }
    .status-card .text-info { color: var(--info-color) !important; }
    .status-card .text-danger { color: var(--danger-color) !important; }

    /* Tab Styling */
    .nav-pills .nav-link {
      color: var(--secondary-color);
      font-weight: 500;
      border-radius: var(--border-radius);
      padding: 0.6rem 1.2rem;
      transition: background-color 0.2s ease, color 0.2s ease;
    }
    .nav-pills .nav-link.active {
      background-color: var(--primary-color);
      color: white;
      box-shadow: 0 2px 5px rgba(13, 110, 253, 0.3);
    }
    .nav-pills .nav-link:not(.active):hover {
      background-color: var(--medium-gray);
    }

    /* Table Styling */
    .table {
      border-collapse: separate;
      border-spacing: 0;
      margin-bottom: 0; /* Remove default margin */
    }
    .table th, .table td {
      padding: 0.9rem 1rem; /* Increased padding */
      vertical-align: middle;
      font-size: 0.9rem;
      white-space: nowrap;
    }
    .table thead th {
      background-color: var(--light-gray);
      color: var(--dark-gray);
      font-weight: 600;
      border-bottom: 2px solid var(--medium-gray);
      text-align: left;
    }
    .table tbody tr {
      background-color: var(--card-bg);
      transition: background-color 0.15s ease-in-out;
    }
    .table tbody tr:hover {
      background-color: #f1f4f9; /* Subtle hover effect */
    }
    .table-responsive {
      border: 1px solid var(--medium-gray);
      border-radius: var(--border-radius);
      overflow-x: auto; /* Enable horizontal scroll */
      overflow-y: hidden;
      width: 100%;
      /* Ensures border radius applies to table */
    }

    /* Button Styling in Table */
    .table .btn {
      padding: 0.3rem 0.6rem; /* Smaller padding for table buttons */
      font-size: 0.8rem;
      margin-right: 0.3rem;
      min-width: 85px; /* Ensure buttons have minimum width */
      text-align: center;
    }
    .table .btn i {
      margin-right: 0.3rem;
    }
    .table .btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    .btn-action-group {
        display: flex;
        gap: 0.5rem; /* Space between review/regenerate */
    }

    /* Status Cell Styling */
    .status-cell .badge {
        font-size: 0.8rem;
        padding: 0.4em 0.7em;
    }

    /* Utility Classes */
    .fw-medium { font-weight: 500; }
    .fw-semibold { font-weight: 600; }
  </style>
</head>
<body>
  <!-- Navbar -->
  <nav class="navbar navbar-expand-lg navbar-light">
    <div class="container-fluid">
      <a class="navbar-brand" href="#">
        <img src="{{ url_for('static', filename='logo_integrum.jpg') }}" alt="Integrum Logo" />
      </a>
      <button id="logoutBtn" class="btn btn-outline-danger btn-sm">
        <i class="fas fa-sign-out-alt me-1"></i>Logout
      </button>
    </div>
  </nav>

  <!-- Main Content Area -->
  <div class="container-xl main-content">
    <h1 class="page-title">DGR Validation Dashboard</h1>

    <!-- Filter and Status Card -->
    <div class="card">
      <div class="card-body">
        <div class="row g-3 align-items-end mb-4">
          <div class="col-md-4 col-lg-3">
            <label for="startDateInput" class="form-label">Start Date</label>
            <input type="date" id="startDateInput" class="form-control form-control-sm" />
          </div>
          <div class="col-md-4 col-lg-3">
            <label for="endDateInput" class="form-label">End Date</label>
            <input type="date" id="endDateInput" class="form-control form-control-sm" />
          </div>
          <div class="col-md-2 col-lg-2">
            <button id="filterBtn" class="btn btn-primary btn-sm w-100">
              <i class="fas fa-filter me-1"></i>Filter
            </button>
          </div>
        </div>

        <!-- Status Count Boxes -->
        {% set status_icons = {
          'Sent': 'fa-check-circle',
          'Sent Updated': 'fa-check-double',
          'Pending': 'fa-clock',
          'In Review': 'fa-search',
          'Saved': 'fa-save'
        } %}
        {% set status_colors = {
          'Sent': 'success',
          'Sent Updated': 'info',
          'Pending': 'secondary',
          'In Review': 'warning',
          'Saved': 'info'
        } %}

        {% for type, counts in status_counts.items() %}
          <div id="statusCounts{{ type|capitalize }}" class="row g-2 mt-3" style="display: {% if type == 'both' %}flex{% else %}none{% endif %};">
            {% for status, count in counts.items() %}
            {% if status in ['Sent', 'Sent Updated', 'Pending', 'In Review', 'Saved'] %}
            <div class="col-6 col-sm-4 col-md-3 col-lg"> <!-- Responsive columns -->
              <div class="status-card border-{{ status_colors[status] }}">
                <i class="fas {{ status_icons[status] }} status-icon text-{{ status_colors[status] }}"></i>
                <div>
<div class="status-text" data-status="{{ status }}">{{ status }}</div>
                  <div class="status-count text-{{ status_colors[status] }}">{{ count }}</div>
                </div>
              </div>
            </div>
            {% endif %}
            {% endfor %}
          </div>
        {% endfor %}
      </div>
    </div>

    <!-- Tabs -->
    <ul class="nav nav-pills mb-3" id="reportTabs" role="tablist">
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="wind-tab" data-bs-toggle="pill" data-bs-target="#Wind" type="button" role="tab" aria-controls="Wind" aria-selected="false" onclick="openTab(event, 'Wind')">
          <i class="fas fa-wind me-1"></i>Wind
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="solar-tab" data-bs-toggle="pill" data-bs-target="#Solar" type="button" role="tab" aria-controls="Solar" aria-selected="false" onclick="openTab(event, 'Solar')">
          <i class="fas fa-sun me-1"></i>Solar
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link active" id="both-tab" data-bs-toggle="pill" data-bs-target="#Both" type="button" role="tab" aria-controls="Both" aria-selected="true" onclick="openTab(event, 'Both')">
          <i class="fas fa-plug me-1"></i>Both
        </button>
      </li>
      <li class="nav-item ms-auto me-1">
        <button id="reportStatusBtn" class="nav-link bg-primary text-white border-0" style="border-radius: 0.375rem;">
            <i class="fas fa-flag me-1"></i>Report status
        </button>
      </li>
      <li class="nav-item me-2">
        <button id="alarmsDataBtn" class="nav-link bg-danger text-white border-0" style="border-radius: 0.375rem;">
            <i class="fas fa-bell me-1"></i>Alarms Data
        </button>
      </li>
      <li class="nav-item me-2">
        <button id="dataAnalysisBtn" class="nav-link bg-warning text-dark border-0" style="border-radius: 0.375rem;">
            <i class="fas fa-chart-bar me-1"></i>Data Analysis
        </button>
      </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content" id="reportTabsContent">
      <!-- Wind Tab -->
      <div class="tab-pane fade" id="Wind" role="tabpanel" aria-labelledby="wind-tab">
        <div class="card">
          <div class="card-header">Wind Reports</div>
          <div class="card-body p-0"> <!-- Remove padding for full-width table -->
            <div class="table-responsive">
              <table class="table table-hover align-middle">
<thead>
                  <tr>
                    <th>Date</th>
                    <th>Plant</th>
                    <th>Actions</th>
                    <th>Status</th>
                    <th>Generation (Daily)</th>
                    <th>Generation (Monthly)</th>
                    <th>Wind Speed (Daily)</th>
                    <th>Wind Speed (Monthly)</th>
                  </tr>
                </thead>
                <tbody id="windReportTableBody"></tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Solar Tab -->
      <div class="tab-pane fade" id="Solar" role="tabpanel" aria-labelledby="solar-tab">
        <div class="card">
          <div class="card-header">Solar Reports</div>
           <div class="card-body p-0">
            <div class="table-responsive">
              <table class="table table-hover align-middle">
<thead>
                  <tr>
                    <th>Date</th>
                    <th>Plant</th>
                    <th>Actions</th>
                    <th>Status</th>
                    <th>Generation (Daily)</th>
                    <th>Generation (Monthly)</th>
                    <th>PR (Daily)</th>
                    <th>PR (Monthly)</th>
                    <th>POA (Daily)</th>
                    <th>POA (Monthly)</th>
                  </tr>
                </thead>
                <tbody id="solarReportTableBody"></tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Both Tab -->
      <div class="tab-pane fade show active" id="Both" role="tabpanel" aria-labelledby="both-tab">
        <div class="card">
          <div class="card-header">Combined Wind & Solar Reports</div>
           <div class="card-body p-0">
            <div class="table-responsive">
              <table class="table table-hover align-middle">
<thead>
                  <tr>
                    <th>Date</th>
                    <th>Solar Plant</th>
                    <th>Actions</th>
                    <th>Status</th>
                    <th>Gen Solar (Daily)</th>
                    <th>Gen Solar (Monthly)</th>
                    <th>PR (Daily)</th>
                    <th>PR (Monthly)</th>
                    <th>POA (Daily)</th>
                    <th>POA (Monthly)</th>
                    <th>Wind Plant</th>
                    <th>Gen Wind (Daily)</th>
                    <th>Gen Wind (Monthly)</th>
                    <th>Wind Speed (Daily)</th>
                    <th>Wind Speed (Monthly)</th>
                  </tr>
                </thead>
                <tbody id="bothReportTableBody"></tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Data Analysis Modal -->
  <div class="modal fade" id="dataAnalysisModal" tabindex="-1" aria-labelledby="dataAnalysisModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header">
<h5 class="modal-title" id="dataAnalysisModalLabel">
            <i class="fas fa-chart-bar me-2"></i>Data Analysis - Prescinto vs Edited Values
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>

        <div class="modal-body">
          <!-- Date Filters for Data Analysis Modal -->
          <form class="row g-3 mb-3 align-items-end" id="dataAnalysisFilters">
            <div class="col-md-3">
              <label for="dataAnalysisStartDate" class="form-label">Start Date</label>
              <input type="date" id="dataAnalysisStartDate" class="form-control" />
            </div>
            <div class="col-md-3">
              <label for="dataAnalysisEndDate" class="form-label">End Date</label>
              <input type="date" id="dataAnalysisEndDate" class="form-control" />
            </div>
            <div class="col-md-2">
              <button type="button" id="dataAnalysisFilterBtn" class="btn btn-primary w-100" style="margin-top: 1.95rem;">
                <i class="fas fa-filter me-1"></i>Filter
              </button>
            </div>
          </form>
          <div id="analysisLoading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Analyzing data...</p>
          </div>
          <div id="analysisContent" style="display: none;">
            <div class="row mb-3">
              <div class="col-md-4">
                <label for="analysisPlant" class="form-label">Select Plant:</label>
                <select id="analysisPlant" class="form-select">
                  <option value="all">All Plants</option>
                  <!-- Options will be populated dynamically -->
                </select>
              </div>
            </div>
            <div class="mb-3 d-flex gap-2">
              <button type="button" id="showAllDatesChartBtn" class="btn btn-outline-primary">
                <i class="fas fa-chart-bar me-1"></i>Show All Dates Chart
              </button>
              <button type="button" id="showPendingSentDiffBtn" class="btn btn-outline-warning">
                <i class="fas fa-chart-bar me-1"></i>Show Pending vs Sent Difference
              </button>
              <button type="button" id="show30DayTrendBtn" class="btn btn-outline-success">
                <i class="fas fa-chart-line me-1"></i>Shows last 30 days trend
              </button>
              <button type="button" id="showGenerationDiffBtn" class="btn btn-outline-danger">
                <i class="fas fa-barcode me-1"></i>Show Generation Diff (Original vs Edited)
              </button>
            </div>
            <div class="chart-container" style="position: relative; height: 400px;">
              <canvas id="analysisChart"></canvas>
            </div>
            <div class="chart-container mt-4" style="position: relative; height: 350px; display: none;" id="generationDiffChartContainer">
              <canvas id="generationDiffChart"></canvas>
            </div>
            <div class="chart-container mt-4" style="position: relative; height: 350px; display: none;" id="generationDiffChartContainerSolar">
              <canvas id="generationDiffChartSolar"></canvas>
            </div>
            <div class="chart-container mt-4" style="position: relative; height: 350px; display: none;" id="generationDiffChartContainerWind">
              <canvas id="generationDiffChartWind"></canvas>
            </div>
            <div class="chart-container mt-4" style="position: relative; height: 200px; display: none;" id="pendingSentDiffChartContainer">
              <canvas id="pendingSentDiffChart"></canvas>
            </div>
            <div class="chart-container mt-4" style="position: relative; height: 350px; display: none;" id="trend30DayChartContainer">
              <canvas id="trend30DayChart"></canvas>
            </div>
            <div id="analysisStats" class="mt-3">
              <!-- Statistics will be displayed here -->
            </div>
          </div>
          <div id="noDataMessage" style="display: none;" class="text-center py-4">
            <i class="fas fa-info-circle text-info fa-3x mb-3"></i>
            <h5>No Edited Data Found</h5>
            <p class="text-muted">No records found for the current tab.</p>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          <button type="button" id="exportAnalysisBtn" class="btn btn-primary">
            <i class="fas fa-download me-1"></i>Export Chart
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <!-- No DateRangePicker needed for single date -->

  <script>
    // Function to switch tabs and update status counts visibility
    function openTab(evt, tabName) {
      // Hide all status count containers
      document.getElementById("statusCountsWind").style.display = "none";
      document.getElementById("statusCountsSolar").style.display = "none";
      document.getElementById("statusCountsBoth").style.display = "none";

      // Show the correct status count container
      document.getElementById(`statusCounts${tabName}`).style.display = "flex"; // Use flex for row layout

      // Bootstrap handles tab content visibility via data-bs-target, no manual JS needed for that part
      // However, we need to ensure the correct tab content pane is marked as 'active' and 'show'
      // This might be handled by Bootstrap automatically when clicking the button,
      // but let's ensure our JS doesn't interfere if we were manually managing it.
    }

    // Function to get status badge class
    function getStatusBadge(status) {
        const statusMap = {
          'Sent': { class: 'success', icon: 'fa-check-circle' },
          'Sent Updated': { class: 'info', icon: 'fa-check-double' },
          'Pending': { class: 'secondary', icon: 'fa-clock' },
          'In Review': { class: 'warning', icon: 'fa-search' },
          'Regenerated': { class: 'info', icon: 'fa-sync-alt' },
          'Not Sent': { class: 'danger', icon: 'fa-ban' },
          'Saved': { class: 'info', icon: 'fa-save' },
          'Regenerating...': { class: 'primary', icon: 'fa-spinner fa-spin' }
        };
        const config = statusMap[status] || { class: 'light', icon: 'fa-question-circle' };
        return `<span class="badge bg-${config.class}"><i class="fas ${config.icon} me-1"></i>${status}</span>`;
    }


    $(document).ready(function () {
      // Logout functionality
      $('#logoutBtn').click(function () {
        window.location.href = '/logout';
      });

      // Set default start and end date to yesterday
      const yesterday = moment().subtract(1, 'days').format('YYYY-MM-DD');
      $('#startDateInput').val(yesterday);
      $('#endDateInput').val(yesterday);

      // Filter button click handler
      $('#filterBtn').click(function () {
        const $filterBtn = $(this);
        const startDate = $('#startDateInput').val();
        const endDate = $('#endDateInput').val();
        if (!startDate || !endDate) {
            alert('Please select both start and end dates.');
            return;
        }
        if (endDate < startDate) {
            alert('End Date cannot be before Start Date.');
            return;
        }

        // Loading state
        const originalButtonHtml = $filterBtn.html();
        $filterBtn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading...');

        const endpoints = ['wind', 'solar', 'both'];
        let fetchPromises = [];

        endpoints.forEach(type => {
          const tbody = $(`#${type}ReportTableBody`);
          // More professional loading state
          const colspan = (type === 'wind') ? 7 : (type === 'solar' ? 8 : 11);
          tbody.empty().html(`<tr><td colspan="${colspan}" class="text-center text-muted py-4"><div class="spinner-border spinner-border-sm me-2" role="status"></div>Loading data...</td></tr>`);

          const promise = $.ajax({
            url: `/api/${type}`,
            method: 'GET',
            data: { start_date: startDate, end_date: endDate }
          });

          promise.done(function(data) {
              tbody.empty(); // Clear loading message

              if (!data || data.length === 0) {
                  tbody.html(`<tr><td colspan="${colspan}" class="text-center text-muted py-4">No data found for this date.</td></tr>`);
                  return;
              }

              data.forEach(report => {
                // Define disabling conditions based on prev_index.html logic
                const isApproveDisabled = report.action_performed || report.status === 'Not Sent';
                const isReviewDisabled = report.action_performed && report.status !== 'Not Sent';
                const isRegenDisabled = report.regenerate || (report.action_performed && report.status !== 'Not Sent');
                const isDontSendDisabled = report.status === 'Not Sent' || report.action_performed || report.status === 'Sent';

                // Show Regen button only for 'In Review'
                const showRegen = report.status === 'In Review';

                // Generate buttons using the specific conditions
const approveBtn = `<button class="btn btn-success btn-sm approve-btn" style="display:none;" data-id="${report.id}" data-type="${type}" ${isApproveDisabled ? 'disabled' : ''}><i class="fas fa-check"></i>Approve</button>`;
                const reviewBtn = `<button class="btn btn-warning btn-sm review-btn" data-id="${report.id}" data-type="${type}" data-report='${JSON.stringify(report).replace(/'/g, "'")}' ${isReviewDisabled ? 'disabled' : ''}><i class="fas fa-search"></i>Review</button>`;
const whatsappStatusBtn = (report.status === "Sent" || report.status === "Sent Updated")
  ? `<button class="btn btn-info btn-sm whatsapp-status-btn" data-id="${report.id}" data-type="${type}"><i class="fab fa-whatsapp"></i>Status</button>`
  : "";
                const dontSendBtn = `<button class="btn btn-danger btn-sm dont-send-btn d-none" data-id="${report.id}" data-type="${type}" ${isDontSendDisabled ? 'disabled' : ''}><i class="fas fa-ban"></i>Don't Send</button>`;

                let rowHtml;
const actionsHtml = `
                    <div class="d-flex flex-nowrap gap-1">
                        ${approveBtn}
                        ${reviewBtn}
                        ${whatsappStatusBtn}
                        ${dontSendBtn}
                    </div>`;

if (
  report.status === "Sent Updated" ||
  report.save_action === true ||
  report.edit_action === true
) {
  // Helper for fallback logic
  function showEdit(val, editVal) {
    return (editVal !== undefined && editVal !== null && editVal !== "") ? editVal : (val ?? "");
  }
  if (type === 'wind') {
    rowHtml = `<tr data-row-id="${report.id}" data-row-type="${type}">
        <td>${report.date}</td>
        <td>${report.plant_long_name} <span class="text-muted">(${report.plant_short_name})</span></td>
        <td>${actionsHtml}</td>
        <td class="status-cell">${getStatusBadge(report.status)}</td>
        <td>${showEdit(report.generation, report.edit_generation)}</td>
        <td>${showEdit(report.generation_monthly, report.edit_generation_monthly)}</td>
        <td>${showEdit(report.wind_speed, report.edit_wind_speed)}</td>
        <td>${showEdit(report.wind_speed_monthly, report.edit_wind_speed_monthly)}</td>
    </tr>`;
  } else if (type === 'solar') {
    rowHtml = `<tr data-row-id="${report.id}" data-row-type="${type}">
        <td>${report.date}</td>
        <td>${report.plant_long_name} <span class="text-muted">(${report.plant_short_name})</span></td>
        <td>${actionsHtml}</td>
        <td class="status-cell">${getStatusBadge(report.status)}</td>
        <td>${showEdit(report.generation, report.edit_generation)}</td>
        <td>${showEdit(report.generation_monthly, report.edit_generation_monthly)}</td>
        <td>${showEdit(report.pr, report.edit_pr)}</td>
        <td>${showEdit(report.pr_monthly, report.edit_pr_monthly)}</td>
        <td>${showEdit(report.poa, report.edit_poa)}</td>
        <td>${showEdit(report.poa_monthly, report.edit_poa_monthly)}</td>
    </tr>`;
  } else { // Both
    rowHtml = `<tr data-row-id="${report.id}" data-row-type="${type}">
        <td>${report.date}</td>
        <td>${report.plant_long_name_solar} <span class="text-muted">(${report.plant_short_name_solar})</span></td>
        <td>${actionsHtml}</td>
        <td class="status-cell">${getStatusBadge(report.status)}</td>
        <td>${showEdit(report.generation_solar, report.edit_generation_solar)}</td>
        <td>${showEdit(report.generation_solar_monthly, report.edit_generation_solar_monthly)}</td>
        <td>${showEdit(report.pr, report.edit_pr)}</td>
        <td>${showEdit(report.pr_monthly, report.edit_pr_monthly)}</td>
        <td>${showEdit(report.poa, report.edit_poa)}</td>
        <td>${showEdit(report.poa_monthly, report.edit_poa_monthly)}</td>
        <td>${report.plant_long_name_wind} <span class="text-muted">(${report.plant_short_name_wind})</span></td>
        <td>${showEdit(report.generation_wind, report.edit_generation_wind)}</td>
        <td>${showEdit(report.generation_wind_monthly, report.edit_generation_wind_monthly)}</td>
        <td>${showEdit(report.wind_speed, report.edit_wind_speed)}</td>
        <td>${showEdit(report.wind_speed_monthly, report.edit_wind_speed_monthly)}</td>
    </tr>`;
  }
} else {
  if (type === 'wind') {
    rowHtml = `<tr data-row-id="${report.id}" data-row-type="${type}">
        <td>${report.date}</td>
        <td>${report.plant_long_name} <span class="text-muted">(${report.plant_short_name})</span></td>
        <td>${actionsHtml}</td>
        <td class="status-cell">${getStatusBadge(report.status)}</td>
        <td>${report.generation ?? ''}</td>
        <td>${report.generation_monthly ?? ''}</td>
        <td>${report.wind_speed ?? ''}</td>
        <td>${report.wind_speed_monthly ?? ''}</td>
    </tr>`;
  } else if (type === 'solar') {
    rowHtml = `<tr data-row-id="${report.id}" data-row-type="${type}">
        <td>${report.date}</td>
        <td>${report.plant_long_name} <span class="text-muted">(${report.plant_short_name})</span></td>
        <td>${actionsHtml}</td>
        <td class="status-cell">${getStatusBadge(report.status)}</td>
        <td>${report.generation ?? ''}</td>
        <td>${report.generation_monthly ?? ''}</td>
        <td>${report.pr ?? ''}</td>
        <td>${report.pr_monthly ?? ''}</td>
        <td>${report.poa ?? ''}</td>
        <td>${report.poa_monthly ?? ''}</td>
    </tr>`;
  } else { // Both
    rowHtml = `<tr data-row-id="${report.id}" data-row-type="${type}">
        <td>${report.date}</td>
        <td>${report.plant_long_name_solar} <span class="text-muted">(${report.plant_short_name_solar})</span></td>
        <td>${actionsHtml}</td>
        <td class="status-cell">${getStatusBadge(report.status)}</td>
        <td>${report.generation_solar ?? ''}</td>
        <td>${report.generation_solar_monthly ?? ''}</td>
        <td>${report.pr ?? ''}</td>
        <td>${report.pr_monthly ?? ''}</td>
        <td>${report.poa ?? ''}</td>
        <td>${report.poa_monthly ?? ''}</td>
        <td>${report.plant_long_name_wind} <span class="text-muted">(${report.plant_short_name_wind})</span></td>
        <td>${report.generation_wind ?? ''}</td>
        <td>${report.generation_wind_monthly ?? ''}</td>
        <td>${report.wind_speed ?? ''}</td>
        <td>${report.wind_speed_monthly ?? ''}</td>
    </tr>`;
  }
}
tbody.append(rowHtml);
              });
          });

          promise.fail(function(jqXHR, textStatus, errorThrown) {
              console.error(`Error fetching ${type} data:`, textStatus, errorThrown);
              tbody.empty().html(`<tr><td colspan="${colspan}" class="text-center text-danger py-4">Error loading ${type} data. Please try again.</td></tr>`);
          });

          fetchPromises.push(promise);
        });

        // Fetch and update status counts (top boxes)
        $.ajax({
          url: '/api/status_counts',
          method: 'GET',
          data: { start_date: startDate, end_date: endDate },
          success: function (statusCounts) {
            // For each type (wind, solar, both)
            ['Wind', 'Solar', 'Both'].forEach(function(type) {
              // statusCounts keys are lowercase: wind, solar, both
              var typeKey = type.toLowerCase();
              var container = document.getElementById('statusCounts' + type);
              if (!container) return;
              // For each status card in the container
              $(container).find('.status-card').each(function () {
                var $card = $(this);
                var statusKey = $card.find('.status-text').data('status');
                // statusCounts[typeKey][statusKey] gives the count
                var count = (statusCounts[typeKey] && statusCounts[typeKey][statusKey]) || 0;
                $card.find('.status-count').text(count);
              });
            });
          }
        });

        // Restore button after all AJAX calls complete
        $.when.apply($, fetchPromises).always(function() {
            $filterBtn.prop('disabled', false).html(originalButtonHtml);
        });
      });

      // --- Action Button Handlers ---

      // Approve Click
      $(document).on('click', '.approve-btn', function () {
        const $button = $(this);
        const id = $button.data('id');
        const type = $button.data('type');
        const updateUrl = `/api/${type}/update`;
        const $row = $button.closest('tr');

        // Optimistic UI update (disable buttons immediately)
        $row.find('.approve-btn, .review-btn, .regenerate-btn, .dont-send-btn').prop('disabled', true);

        $.ajax({
          url: updateUrl,
          method: 'POST',
          contentType: 'application/json',
          data: JSON.stringify({ ids: [id], approved: true, review: false }),
          success: function (res) {
            // Update status cell
            $row.find('.status-cell').html(getStatusBadge(res.status));

            // Refresh status counts (top boxes) after status change
            updateStatusCounts();

            // Finalize button states based on action
            $row.find('.regenerate-btn').prop('disabled', true); // Disable regen on approve
            $row.find('.dont-send-btn').prop('disabled', true); // Disable Don't Send on approve
            // No alert needed, visual feedback is sufficient
          },
          error: function(jqXHR, textStatus, errorThrown) {
             console.error("Error updating report:", textStatus, errorThrown);
             alert("Error updating report status. Please try again.");
             // Revert optimistic UI changes on error? (More complex)
             // For now, just log and alert. Refresh might be needed.
             $('#filterBtn').trigger('click'); // Refresh data on error
          }
        });
      });

      // WhatsApp Status Click - Show WhatsApp send status for this report
      $(document).on('click', '.whatsapp-status-btn', function () {
        const $button = $(this);
        const id = $button.data('id');
        const type = $button.data('type');
        $button.prop('disabled', true);

        // Show modal immediately with loading spinner
        $('#whatsappStatusModalBody').html('<div class="text-center py-4"><div class="spinner-border text-info" role="status"></div><div>Loading status...</div></div>');
        const modal = new bootstrap.Modal(document.getElementById('whatsappStatusModal'));
        modal.show();

        $.ajax({
          url: `/api/whatsapp-status/${type}/${id}`,
          method: 'GET',
success: function(resp) {
            console.log("WhatsApp status rows:", resp.rows);
            let html;
            if (resp.success && Array.isArray(resp.rows) && resp.rows.length > 0) {
              html = `<div class="table-responsive"><table class="table table-bordered table-sm mb-0">
<thead>
                  <tr>
                    <th>Sent Date</th>
                    <th>Name</th>
                    <th>Number</th>
                    <th>Status</th>
                  </tr>
                </thead>
                <tbody>
                  ${resp.rows.map(row => `
                    <tr>
                      <td>${row.send_date ? row.send_date.split('T')[0] : ""}</td>
                      <td>${row.contact_person || "N/A"}</td>
                      <td>${row.recipient_id || "N/A"}</td>
                      <td><span class="badge bg-info text-dark">${row.status || "N/A"}</span></td>
                    </tr>
                  `).join('')}
                </tbody>
              </table></div>`;
            } else if (resp.status === "Not Found" || (Array.isArray(resp.rows) && resp.rows.length === 0)) {
              html = `<div class="text-center"><i class="fab fa-whatsapp"></i> <span class="badge bg-secondary">No WhatsApp Status</span></div>`;
            } else {
              html = `<div class="text-center"><i class="fab fa-whatsapp"></i> <span class="badge bg-danger">Error</span></div>`;
            }
            $('#whatsappStatusModalBody').html(html);
          },
          error: function() {
            $('#whatsappStatusModalBody').html('<div class="text-center"><i class="fab fa-whatsapp"></i> <span class="badge bg-danger">Error</span></div>');
          },
          complete: function() {
            setTimeout(() => {
              $button.prop('disabled', false);
            }, 1000);
          }
        });

        // Removed code that updates the WhatsApp status button HTML after fetching status.
        // The button will always remain as a normal button (icon + "Status" text).
        // Only the modal will show the status details.
        setTimeout(() => {
          $button.prop('disabled', false);
        }, 2000);
      });

      // Review Click - Now includes edit functionality
      $(document).on('click', '.review-btn', function () {
        const $button = $(this);
        const id = $button.data('id');
        const type = $button.data('type');
        const updateUrl = `/api/${type}/update`;
        const $row = $button.closest('tr');
        
        // Get report data for edit modal
        let report;
        try {
          report = JSON.parse($button.attr('data-report').replace(/'/g, "'"));
        } catch (e) {
          $('#editFormFields').html('<div class="alert alert-danger">Failed to load report data.</div>');
          $('#editModal').modal('show');
          return;
        }

        // First update status to review
        $.ajax({
          url: updateUrl,
          method: 'POST',
          contentType: 'application/json',
          data: JSON.stringify({ ids: [id], approved: false, review: true }),
          success: function (res) {
            // Update status cell
            $row.find('.status-cell').html(getStatusBadge(res.status));

            // Refresh status counts (top boxes) after status change
            updateStatusCounts();

            // Show regenerate button on review
            $row.find('.regenerate-btn').removeClass('d-none');
            
            // Keep review button enabled
            $row.find('.review-btn').prop('disabled', false);

            // Set up edit context and show modal
            currentEditContext = { id: id, type: type, report: report };

            // Generate and inject form fields
            $('#editFormFields').html(getEditFormFields(report, type));
            $('#editFormError').addClass('d-none').text('');
            $('#editFormSuccess').addClass('d-none').text('');
            $('#editFormSubmitBtn').prop('disabled', false);
            
            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('editModal'));
            modal.show();
          },
          error: function(jqXHR, textStatus, errorThrown) {
             console.error("Error updating report:", textStatus, errorThrown);
             alert("Error updating report status. Please try again.");
             $('#filterBtn').trigger('click'); // Refresh data on error
          }
        });
      });

      

      // --- Professional "Don't Send" Modal ---
      // Modal HTML
      if (!document.getElementById('dontSendModal')) {
        $('body').append(`
          <div class="modal fade" id="dontSendModal" tabindex="-1" aria-labelledby="dontSendModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title" id="dontSendModalLabel"><i class="fas fa-ban text-danger me-2"></i>Mark as "Not Sent"</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                  <label for="dontSendComment" class="form-label fw-semibold">Please provide a reason for marking this report as <span class="text-danger">"Not Sent"</span>:</label>
                  <textarea id="dontSendComment" class="form-control" rows="3" maxlength="300" placeholder="Enter your comments here..." style="resize: vertical;"></textarea>
                  <div class="form-text text-end"><span id="commentCharCount">0</span>/300 characters</div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                  <button type="button" class="btn btn-danger" id="submitDontSendBtn">Submit</button>
                </div>
              </div>
            </div>
          </div>
        `);
      }

      let dontSendContext = {};
      $(document).on('click', '.dont-send-btn', function () {
        const $button = $(this);
        const id = $button.data('id');
        const type = $button.data('type');
        const $row = $button.closest('tr');
        dontSendContext = { $button, id, type, $row };

        $('#dontSendComment').val('');
        $('#commentCharCount').text('0');
        const modal = new bootstrap.Modal(document.getElementById('dontSendModal'));
        modal.show();
      });

      // Character count for textarea
      $(document).on('input', '#dontSendComment', function () {
        $('#commentCharCount').text($(this).val().length);
      });

      // Handle submit in modal
      $(document).on('click', '#submitDontSendBtn', function () {
        const comments = $('#dontSendComment').val().trim();
        if (!comments) {
          $('#dontSendComment').addClass('is-invalid');
          $('#dontSendComment').focus();
          return;
        }
        $('#dontSendComment').removeClass('is-invalid');
        const { $button, id, type, $row } = dontSendContext;
        const updateUrl = `/api/${type}/update`;

        // Optimistic UI
        $button.prop('disabled', true);
        $row.find('.approve-btn').prop('disabled', true);
        $row.find('.status-cell').html(getStatusBadge('Not Sent'));

        // Hide modal
        bootstrap.Modal.getInstance(document.getElementById('dontSendModal')).hide();

        $.ajax({
          url: updateUrl,
          method: 'POST',
          contentType: 'application/json',
          data: JSON.stringify({ ids: [id], dont_send: true, comments: comments, status: "Not Sent" }),
          success: function (res) {
            // Status already updated optimistically
            console.log("Marked as 'Not Sent'");
            // Refresh status counts (top boxes) after status change
            updateStatusCounts();
          },
          error: function (jqXHR, textStatus, errorThrown) {
            alert("Failed to mark as 'Not Sent'. Please try again.");
            console.error("Don't Send error:", textStatus, errorThrown);
            $('#filterBtn').trigger('click');
          }
        });
      });

      // Helper function to update status counts (top boxes)
      function updateStatusCounts() {
        const startDate = $('#startDateInput').val();
        const endDate = $('#endDateInput').val();
        $.ajax({
          url: '/api/status_counts',
          method: 'GET',
          data: { start_date: startDate, end_date: endDate },
          success: function (statusCounts) {
            ['Wind', 'Solar', 'Both'].forEach(function(type) {
              var typeKey = type.toLowerCase();
              var container = document.getElementById('statusCounts' + type);
              if (!container) return;
              $(container).find('.status-card').each(function () {
                var $card = $(this);
                var statusText = $card.find('.status-text').text().trim();
                var count = (statusCounts[typeKey] && statusCounts[typeKey][statusText]) || 0;
                $card.find('.status-count').text(count);
              });
            });
          }
        });
      }

      // Initial data load
      $('#filterBtn').trigger('click');

      // Set initial active tab's status counts visibility
      // Since 'Both' is active by default, trigger its display logic
      openTab(null, 'Both');

      // Ensure the correct tab content is shown on load (Bootstrap might handle this, but being explicit)
      $('#both-tab').tab('show');

      // --- Edit Report Modal Logic ---
      function getEditFormFields(report, type) {
        function valueRow(label, value) {
          return `<div class='edit-row'><span class='edit-label'>${label}</span><span class='edit-value'>${value ?? ''}</span></div>`;
        }
        function inputRow(label, name, value, required = false) {
          return `<div class='edit-row'><label class='edit-label' for='${name}'>${label}</label><input type='number' step='any' class='form-control edit-input' id='${name}' name='${name}' value='${value ?? ''}' ${required ? 'required' : ''}></div>`;
        }
        // Info section
        let infoHtml = '';
        if (type === 'wind') {
          infoHtml += `<div class='edit-section-title'>Info</div>`;
          infoHtml += valueRow('Date', report.date);
          infoHtml += valueRow('Plant Name', report.plant_long_name);
          infoHtml += valueRow('Plant ID', report.plant_short_name ?? (report.plant_id ?? ''));
        } else if (type === 'solar') {
          infoHtml += `<div class='edit-section-title'>Info</div>`;
          infoHtml += valueRow('Date', report.date);
          infoHtml += valueRow('Plant Name', report.plant_long_name);
          infoHtml += valueRow('Plant ID', report.plant_short_name ?? (report.plant_id ?? ''));
        } else if (type === 'both') {
          infoHtml += `<div class='edit-section-title'>Info</div>`;
          infoHtml += valueRow('Date', report.date);
          infoHtml += valueRow('Solar Plant Name', report.plant_long_name_solar);
          infoHtml += valueRow('Solar Plant ID', report.plant_short_name_solar ?? (report.plant_id_solar ?? ''));
          infoHtml += valueRow('Wind Plant Name', report.plant_long_name_wind);
          infoHtml += valueRow('Wind Plant ID', report.plant_short_name_wind ?? (report.plant_id_wind ?? ''));
        }
        let origHtml = '', editHtml = '';
        if (type === 'wind') {
          origHtml += `<div class='edit-section-title'>Wind</div>`;
          origHtml += valueRow('Generation (Kwh)', report.generation);
          origHtml += valueRow('Wind Speed (m/s)', report.wind_speed);
          origHtml += valueRow('Generation Monthly (Kwh)', report.generation_monthly);
          origHtml += valueRow('Wind Speed Monthly (m/s)', report.wind_speed_monthly);
          editHtml += `<div class='edit-section-title'>Wind</div>`;
          editHtml += inputRow('Generation (Kwh)', 'edit_generation', report.edit_generation ?? report.generation, true);
          editHtml += inputRow('Wind Speed (m/s)', 'edit_wind_speed', report.edit_wind_speed ?? report.wind_speed, true);
          editHtml += inputRow('Generation Monthly (Kwh)', 'edit_generation_monthly', report.edit_generation_monthly ?? report.generation_monthly);
          editHtml += inputRow('Wind Speed Monthly (m/s)', 'edit_wind_speed_monthly', report.edit_wind_speed_monthly ?? report.wind_speed_monthly);
        } else if (type === 'solar') {
          origHtml += `<div class='edit-section-title'>Solar</div>`;
          origHtml += valueRow('Generation', report.generation);
          origHtml += valueRow('PR (%)', report.pr);
          origHtml += valueRow('POA (wh/m2)', report.poa);
          origHtml += valueRow('Generation Monthly (Kwh)', report.generation_monthly);
          origHtml += valueRow('PR Monthly (%)', report.pr_monthly);
          origHtml += valueRow('POA Monthly (wh/m2)', report.poa_monthly);
          editHtml += `<div class='edit-section-title'>Solar</div>`;
          editHtml += inputRow('Generation (Kwh)', 'edit_generation', report.edit_generation ?? report.generation, true);
          editHtml += inputRow('PR (%)', 'edit_pr', report.edit_pr ?? report.pr, true);
          editHtml += inputRow('POA (KW/m2)', 'edit_poa', report.edit_poa ?? report.poa, true);
          editHtml += inputRow('Generation Monthly (Kwh)', 'edit_generation_monthly', report.edit_generation_monthly ?? report.generation_monthly);
          editHtml += inputRow('PR Monthly (%)', 'edit_pr_monthly', report.edit_pr_monthly ?? report.pr_monthly);
          editHtml += inputRow('POA Monthly (wh/m2)', 'edit_poa_monthly', report.edit_poa_monthly ?? report.poa_monthly);
        } else if (type === 'both') {
          origHtml += `<div class='edit-section-title'>Solar</div>`;
          origHtml += valueRow('Generation (Kwh)', report.generation_solar);
          origHtml += valueRow('PR (%)', report.pr);
          origHtml += valueRow('POA (KwH/m2)', report.poa);
          origHtml += valueRow('Generation Monthly (Kwh)', report.generation_solar_monthly);
          origHtml += valueRow('PR Monthly (%)', report.pr_monthly);
          origHtml += valueRow('POA Monthly (KwH/m2)', report.poa_monthly);
          origHtml += `<div class='edit-section-title'>Wind</div>`;
          origHtml += valueRow('Generation (Kwh)', report.generation_wind);
          origHtml += valueRow('Wind Speed (m/s)', report.wind_speed);
          origHtml += valueRow('Generation Monthly (Kwh)', report.generation_wind_monthly);
          origHtml += valueRow('Wind Speed Monthly (m/s)', report.wind_speed_monthly);
          editHtml += `<div class='edit-section-title'>Solar</div>`;
          editHtml += inputRow('Generation (Kwh)', 'edit_generation_solar', report.edit_generation_solar ?? report.generation_solar, true);
          editHtml += inputRow('PR (%)', 'edit_pr', report.edit_pr ?? report.pr, true);
          editHtml += inputRow('POA (wh/m2)', 'edit_poa', report.edit_poa ?? report.poa, true);
          editHtml += inputRow('Generation Monthly (Kwh)', 'edit_generation_solar_monthly', report.edit_generation_solar_monthly ?? report.generation_solar_monthly);
          editHtml += inputRow('PR Monthly (%)', 'edit_pr_monthly', report.edit_pr_monthly ?? report.pr_monthly);
          editHtml += inputRow('POA Monthly (wh/m2)', 'edit_poa_monthly', report.edit_poa_monthly ?? report.poa_monthly);
          editHtml += `<div class='edit-section-title'>Wind</div>`;
          editHtml += inputRow('Generation (Kwh)', 'edit_generation_wind', report.edit_generation_wind ?? report.generation_wind, true);
          editHtml += inputRow('Wind Speed (m/s)', 'edit_wind_speed', report.edit_wind_speed ?? report.wind_speed, true);
          editHtml += inputRow('Generation Monthly (Kwh)', 'edit_generation_wind_monthly', report.edit_generation_wind_monthly ?? report.generation_wind_monthly);
          editHtml += inputRow('Wind Speed Monthly  (m/s)', 'edit_wind_speed_monthly', report.edit_wind_speed_monthly ?? report.wind_speed_monthly);
        }
        // Add comments textarea to the edited values section
        const commentValue = report.edit_comments ?? report.comments ?? '';
        
        // Add Turbine-wise Data tables for wind reports
        function renderTurbineTable(data, editable, originalData) {
          if (!Array.isArray(data) || data.length === 0) return '<div class="text-muted">No turbine data available.</div>';
          let rows = '';
          data.forEach((turbine, idx) => {
            const locNo = turbine['Loc No'] ?? '';
            const windSpeed = turbine['Avg Wind Speed'] ?? '';
const generation = turbine['Daily Generation (kWh)'] ?? turbine['Daily Generation (KWh)'] ?? '';
            if (editable) {
              // Find original for diff/highlight
              let orig = (originalData || []).find(t => t['Loc No'] === locNo) || {};
              const windSpeedOrig = orig['Avg Wind Speed'] ?? '';
const generationOrig = orig['Daily Generation (kWh)'] ?? orig['Daily Generation (KWh)'] ?? '';
              const windSpeedChanged = String(windSpeed) !== String(windSpeedOrig);
              const generationChanged = String(generation) !== String(generationOrig);
              rows += `<tr${(windSpeedChanged || generationChanged) ? ' class="table-warning"' : ''}>
                <td>${locNo}</td>
                <td>
                  <input type="number" step="any" min="0" class="form-control form-control-sm turbine-edit-wind-speed" name="turbine_wind_speed_${idx}" value="${windSpeed}" data-original="${windSpeedOrig}" required>
                </td>
                <td>
                  <input type="number" step="any" min="0" class="form-control form-control-sm turbine-edit-generation" name="turbine_generation_${idx}" value="${generation}" data-original="${generationOrig}" required>
                </td>
              </tr>`;
            } else {
              rows += `<tr>
                <td>${locNo}</td>
                <td>${windSpeed}</td>
                <td>${generation}</td>
              </tr>`;
            }
          });
          return `
            <div class="mt-3">
              <div class="edit-section-title mb-2">Turbine-wise Data</div>
              <div class="table-responsive">
                <table class="table table-bordered table-sm mb-0">
                  <thead>
                    <tr>
                      <th>Turbine No</th>
                      <th>Wind Speed</th>
                      <th>Generation</th>
                    </tr>
                  </thead>
                  <tbody>
                    ${rows}
                  </tbody>
                </table>
              </div>
            </div>
          `;
        }

        // Parse turbine-wise data for wind and both reports
        let turbineData = [];
        let turbineEditData = [];
        let useEdit = false;
        if (type === 'wind' || type === 'both') {
          try {
            turbineData = JSON.parse(report.csv_report_data || '[]');
          } catch (e) { turbineData = []; }
          try {
            turbineEditData = JSON.parse(report.edit_csv_report_data || '[]');
            useEdit = report.edit_csv_report_data !== null && report.edit_csv_report_data !== undefined;
          } catch (e) { turbineEditData = []; }
        }

        return `
          <div class='edit-modal-info-box' style="margin-bottom:1.2rem; border:1.5px solid #e0e0e0; border-radius:0.7rem; background:#f8f9fa; padding:1rem 1.2rem;">
            ${infoHtml}
          </div>
          <div class='edit-modal-flex'>
            <div class='edit-original-box'>
              <div class='edit-box-title'>Prescinto Values</div>
              ${origHtml}
              ${(type === 'wind' || type === 'both') ? renderTurbineTable(turbineData, false) : ''}
            </div>
            <div class='edit-editable-box'>
              <div class='edit-box-title'>Edited Values</div>
              <form class='edit-form-fields-inner'>
                ${editHtml}
                <div class='edit-row' style="grid-column: 1 / -1;">
                  <label class='edit-label' for='edit_comments' style="grid-column: 1 / 2; align-self: start;">Comments</label>
                  <textarea class='form-control' id='edit_comments' name='edit_comments' rows='2' placeholder='Enter comments (optional)' style="resize: vertical; grid-column: 2 / 3;">${commentValue}</textarea>
                </div>
              </form>
              ${(type === 'wind' || type === 'both') ? renderTurbineTable(useEdit ? turbineEditData : turbineData, true, turbineData) : ''}
            </div>
          </div>
        `;
      }

      let currentEditContext = {};

      // Separate Save and Send button logic for edit modal

      // Save button: Save edited data to DB (edited column), then update status/save_action via backend
      $('#editFormSaveBtn').on('click', function () {
        $('#editFormError').addClass('d-none').text('');
        $('#editFormSuccess').addClass('d-none').text('');
        $('#editFormSaveBtn').prop('disabled', true);
        $('#editFormSaveSpinner').removeClass('d-none');

        // Gather form data
        const formData = {};
        $('#editForm').serializeArray().forEach(function (item) {
          formData[item.name] = item.value;
        });

        // --- Collect Turbine-wise Data for Wind and Both Reports ---
        if (currentEditContext.type === 'wind' || currentEditContext.type === 'both') {
          // Find the editable turbine-wise table in the modal
          const turbineRows = $('#editFormFields .turbine-edit-wind-speed').closest('tr');
          const turbineData = [];
          turbineRows.each(function () {
            const $row = $(this);
            const locNo = $row.find('td').eq(0).text().trim();
            const windSpeed = $row.find('.turbine-edit-wind-speed').val();
            const generation = $row.find('.turbine-edit-generation').val();
            turbineData.push({
              "Loc No": locNo,
              "Avg Wind Speed": windSpeed,
              "Daily Generation (kWh)": generation
            });
          });
          formData.edit_csv_report_data = JSON.stringify(turbineData);
        }
        // --- End Turbine-wise Data Collection ---

        // Get current edit context (id, type)
        const { id, type } = currentEditContext;
        const editUrl = `/api/edit/${type}/${id}`;
        const updateUrl = `/api/${type}/update`;

        // First, save the edited values
        $.ajax({
          url: editUrl,
          method: 'POST',
          contentType: 'application/json',
          data: JSON.stringify(formData),
          success: function (res) {
            // Now, update status and save_action via backend
            $.ajax({
              url: updateUrl,
              method: 'POST',
              contentType: 'application/json',
              data: JSON.stringify({ ids: [id], save_action: true }),
              success: function (updateRes) {
                $('#editFormSuccess').removeClass('d-none');
                $('#editFormSuccess .edit-success-check').removeClass('d-none');
                $('#editFormSuccess .edit-success-msg').text('Changes saved successfully!');
                // Update status cell in table to "Saved"
                $(`tr[data-row-id="${id}"][data-row-type="${type}"]`).find('.status-cell').html(getStatusBadge('Saved'));
                setTimeout(function () {
                  $('#editFormSuccess').addClass('d-none');
                  $('#editFormSuccess .edit-success-check').addClass('d-none');
                  $('#editFormSuccess .edit-success-msg').text('');
                  // Close the modal after save
                  const modalEl = document.getElementById('editModal');
                  if (modalEl) {
                    const modalInstance = bootstrap.Modal.getInstance(modalEl);
                    if (modalInstance) modalInstance.hide();
                  }
                  // Trigger filter to refresh all data after save
                  $('#filterBtn').trigger('click');
                }, 1200);
                $('#editFormSaveBtn').prop('disabled', false);
                $('#editFormSaveSpinner').addClass('d-none');
              },
              error: function () {
                $('#editFormError').removeClass('d-none').text('Failed to update status to Saved. Please try again.');
                $('#editFormSaveBtn').prop('disabled', false);
                $('#editFormSaveSpinner').addClass('d-none');
              }
            });
          },
          error: function (jqXHR, textStatus, errorThrown) {
            $('#editFormError').removeClass('d-none').text('Failed to save changes. Please try again.');
            $('#editFormSaveBtn').prop('disabled', false);
            $('#editFormSaveSpinner').addClass('d-none');
          }
        });
      });

      // Send button: Approve/send the report (after save if needed)
      $('#editFormSendBtn').on('click', function () {
        $('#editFormError').addClass('d-none').text('');
        $('#editFormSuccess').addClass('d-none').text('');
        $('#editFormSendBtn').prop('disabled', true);
        $('#editFormSendSpinner').removeClass('d-none');

        // Gather form data (save before send, to ensure latest edits are in DB)
        const formData = {};
        $('#editForm').serializeArray().forEach(function (item) {
          formData[item.name] = item.value;
        });

        // --- Collect Turbine-wise Data for Wind and Both Reports (Send Button) ---
        if (currentEditContext.type === 'wind' || currentEditContext.type === 'both') {
          // Find the editable turbine-wise table in the modal
          const turbineRows = $('#editFormFields .turbine-edit-wind-speed').closest('tr');
          const turbineData = [];
          turbineRows.each(function () {
            const $row = $(this);
            const locNo = $row.find('td').eq(0).text().trim();
            const windSpeed = $row.find('.turbine-edit-wind-speed').val();
            const generation = $row.find('.turbine-edit-generation').val();
            turbineData.push({
              "Loc No": locNo,
              "Avg Wind Speed": windSpeed,
              "Daily Generation (kWh)": generation
            });
          });
          formData.edit_csv_report_data = JSON.stringify(turbineData);
        }
        // --- End Turbine-wise Data Collection (Send Button) ---

        const { id, type, report } = currentEditContext;
        const editUrl = `/api/edit/${type}/${id}`;
        const approveUrl = `/api/${type}/update`;

        // First save, then send ONLY if save succeeds
        $.ajax({
          url: editUrl,
          method: 'POST',
          contentType: 'application/json',
          data: JSON.stringify(formData),
          success: function (editRes) {
            // If edit was skipped (no changes), proceed to send anyway
            if (editRes && editRes.status === "skipped") {
              // No changes, so just send (will be "Sent")
              sendApproveRequest(false);
            } else {
              // Changes made, so send (should be "Sent Updated")
              sendApproveRequest(true);
            }
          },
          error: function () {
            $('#editFormError').removeClass('d-none').text('Failed to save changes. Please try again.');
            $('#editFormSendBtn').prop('disabled', false);
            $('#editFormSendSpinner').addClass('d-none');
          }
        });

        function sendApproveRequest(expectEdited) {
          $.ajax({
            url: approveUrl,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ ids: [id], approved: true }),
            success: function (approveRes) {
              // Use backend status directly for display
              const statusText = approveRes && approveRes.status ? approveRes.status : "Sent";
              $(`tr[data-row-id="${id}"][data-row-type="${type}"]`).find('.status-cell').html(getStatusBadge(statusText));

              $('#editFormSuccess').removeClass('d-none');
              $('#editFormSuccess .edit-success-check').removeClass('d-none');
              $('#editFormSuccess .edit-success-msg').text('Report sent/approved successfully!');
              setTimeout(function () {
                const modalEl = document.getElementById('editModal');
                if (modalEl) {
                  const modalInstance = bootstrap.Modal.getInstance(modalEl);
                  if (modalInstance) modalInstance.hide();
                }
                setTimeout(function () {
                  $('#editFormSuccess').addClass('d-none');
                  $('#editFormSuccess .edit-success-check').addClass('d-none');
                  $('#editFormSuccess .edit-success-msg').text('');
                }, 400);
                $('#filterBtn').trigger('click');
              }, 1200);
              $('#editFormSendBtn').prop('disabled', false);
              $('#editFormSendSpinner').addClass('d-none');
            },
            error: function (jqXHR, textStatus, errorThrown) {
              $('#editFormError').removeClass('d-none').text('Failed to send/approve. Please try again.');
              $('#editFormSendBtn').prop('disabled', false);
              $('#editFormSendSpinner').addClass('d-none');
            }
          });
        }
      });

    });
  </script>
  <!-- WhatsApp Status Modal -->
  <div class="modal fade" id="whatsappStatusModal" tabindex="-1" aria-labelledby="whatsappStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header bg-info text-white">
          <h5 class="modal-title" id="whatsappStatusModalLabel">
            <i class="fab fa-whatsapp me-2"></i>WhatsApp Status
          </h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body" id="whatsappStatusModalBody">
          <!-- Row-wise data will be injected here -->
        </div>
        <div class="modal-footer bg-light">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  </div>
  <!-- Edit Report Modal -->
  <div class="modal fade professional-edit-modal" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
      <div class="modal-content edit-modal-content shadow-lg rounded-5 border-0 animate-modal">
        <form id="editForm" autocomplete="off">
          <div class="modal-header bg-gradient-primary text-white position-relative rounded-top-4" style="background: linear-gradient(90deg, #0d6efd 60%, #0dcaf0 100%); border-bottom: none; min-height: 80px;">
            <div class="d-flex align-items-center w-100">
              <div class="edit-modal-icon d-flex align-items-center justify-content-center rounded-circle bg-white me-3 shadow-sm" style="width:56px;height:56px;">
                <i class="fas fa-pen-fancy fa-xl text-primary"></i>
              </div>
              <div class="flex-grow-1">
                <h4 class="modal-title mb-0 fw-bold" id="editModalLabel" style="letter-spacing:0.5px;">Edit Report</h4>
                <div class="small text-white-50 fw-medium" id="editModalSubtitle">Compare and update report details</div>
              </div>
              <button type="button" class="btn-close btn-close-white ms-auto" data-bs-dismiss="modal" aria-label="Close" style="filter: brightness(1.2);"></button>
            </div>
          </div>
          <div class="modal-body px-4 py-4" style="background:rgba(248,249,250,0.98);backdrop-filter:blur(2px);">
            <!-- Dynamic form fields will be shown here -->
            <div id="editFormFields"></div>
            <div class="alert alert-danger d-none mt-3" id="editFormError" role="alert" aria-live="assertive"></div>
            <div class="alert alert-success d-none mt-3 d-flex align-items-center gap-2" id="editFormSuccess" role="status" aria-live="polite">
              <span class="edit-success-check d-none" style="font-size:1.5rem;"><i class="fas fa-check-circle text-success"></i></span>
              <span class="edit-success-msg"></span>
            </div>
          </div>
          <div class="modal-footer bg-light rounded-bottom-4 border-0 d-flex justify-content-between align-items-center">
            <div class="edit-modal-footer-note text-muted small">
              <i class="fas fa-shield-alt me-1"></i>All changes are logged for audit.
            </div>
            <div>
              <button type="button" class="btn btn-outline-secondary px-4" data-bs-dismiss="modal">
                <i class="fas fa-times me-1"></i>Cancel
              </button>
              <button type="button" class="btn btn-outline-info px-4 me-2" id="editFormResetBtn">
                <i class="fas fa-undo me-1"></i>Reset
              </button>
              <button type="button" class="btn btn-gradient-primary px-4 fw-semibold position-relative" id="editFormSaveBtn" style="background: linear-gradient(90deg, #0d6efd 60%, #0dcaf0 100%); border: none;">
                <span class="spinner-border spinner-border-sm d-none position-absolute start-0 ms-2" id="editFormSaveSpinner" style="top:50%;transform:translateY(-50%);"></span>
                <i class="fas fa-save me-1"></i>Save
              </button>
              <button type="button" class="btn btn-success px-4 fw-semibold position-relative" id="editFormSendBtn" style="margin-left: 0.5rem;">
                <span class="spinner-border spinner-border-sm d-none position-absolute start-0 ms-2" id="editFormSendSpinner" style="top:50%;transform:translateY(-50%);"></span>
                <i class="fas fa-paper-plane me-1"></i>Send
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
  <style>
    .professional-edit-modal .modal-dialog {
      animation: fadeInModal 0.35s cubic-bezier(.4,0,.2,1);
    }
    @keyframes fadeInModal {
      from { transform: translateY(40px) scale(0.98); opacity: 0; }
      to { transform: translateY(0) scale(1); opacity: 1; }
    }
    .edit-modal-content {
      box-shadow: 0 12px 40px rgba(13,110,253,0.16), 0 2px 8px rgba(0,0,0,0.10);
      border-radius: 1.5rem !important;
      overflow: hidden;
      border: 1.5px solid #e9ecef;
      background: rgba(255,255,255,0.98);
      backdrop-filter: blur(2px);
      transition: box-shadow 0.2s;
    }
    .edit-modal-content:focus-within {
      box-shadow: 0 16px 48px rgba(13,110,253,0.22), 0 4px 16px rgba(0,0,0,0.13);
    }
    .edit-modal-icon {
      box-shadow: 0 2px 8px rgba(13,110,253,0.10);
      border: 2.5px solid #e9ecef;
      background: #fff;
      transition: box-shadow 0.2s;
    }
    .edit-modal-section-title {
      border-left: 4px solid #0d6efd;
      padding-left: 0.75rem;
      margin-bottom: 1.25rem;
      background: linear-gradient(90deg, #e9ecef 80%, #f8f9fa 100%);
      border-radius: 0.375rem;
      line-height: 2.1;
      box-shadow: 0 1px 2px rgba(13,110,253,0.04);
    }
    #editFormFields .form-control[readonly] {
      background-color: #f8f9fa !important;
      border: 1.5px solid #e9ecef;
      color: #6c757d;
      font-weight: 500;
    }
    #editFormFields .form-label {
      font-weight: 600;
      color: #0d6efd;
      letter-spacing: 0.02em;
    }
    #editFormFields .mb-3 {
      margin-bottom: 1.1rem !important;
    }
    .btn-gradient-primary {
      background: linear-gradient(90deg, #0d6efd 60%, #0dcaf0 100%);
      color: #fff;
      border: none;
      transition: box-shadow 0.2s, background 0.2s;
      box-shadow: 0 2px 8px rgba(13,110,253,0.10);
      position: relative;
      overflow: hidden;
    }
    .btn-gradient-primary:hover, .btn-gradient-primary:focus {
      background: linear-gradient(90deg, #0b5ed7 60%, #0dcaf0 100%);
      color: #fff;
      box-shadow: 0 4px 16px rgba(13,110,253,0.18);
    }
    .btn-gradient-primary:active {
      background: linear-gradient(90deg, #0a58ca 60%, #0dcaf0 100%);
    }
    .edit-success-check {
      animation: pop-check 0.5s cubic-bezier(.68,-0.55,.27,1.55) both;
      display: inline-block;
    }
    @keyframes pop-check {
      0% { transform: scale(0.5); opacity: 0; }
      60% { transform: scale(1.2); opacity: 1; }
      100% { transform: scale(1); opacity: 1; }
    }
    .edit-modal-footer-note {
      font-size: 0.92rem;
      opacity: 0.85;
      letter-spacing: 0.01em;
      display: flex;
      align-items: center;
      gap: 0.3rem;
    }
    .professional-edit-modal .modal-content {
      animation: modalPopIn 0.32s cubic-bezier(.4,0,.2,1);
    }
    @keyframes modalPopIn {
      0% { transform: scale(0.97) translateY(30px); opacity: 0; }
      100% { transform: scale(1) translateY(0); opacity: 1; }
    }
    .professional-edit-modal .modal-backdrop {
      background: rgba(13,110,253,0.08);
      backdrop-filter: blur(1.5px);
    }
    .edit-modal-content .modal-header {
      box-shadow: 0 2px 8px rgba(13,110,253,0.07);
    }
    .edit-modal-content .modal-footer {
      box-shadow: 0 -1px 4px rgba(13,110,253,0.04);
    }
    .edit-modal-content .modal-footer .btn {
      min-width: 120px;
    }
    .edit-modal-content .modal-footer .btn + .btn {
      margin-left: 0.5rem;
    }
    .edit-modal-content .alert {
      border-radius: 0.5rem;
      font-size: 1rem;
      padding: 0.75rem 1.25rem;
    }
    .edit-modal-content .alert-success {
      background: linear-gradient(90deg, #e9f7ef 80%, #f8f9fa 100%);
      color: #198754;
      border: 1.5px solid #b6ecc7;
    }
    .edit-modal-content .alert-danger {
      background: linear-gradient(90deg, #fff0f0 80%, #f8f9fa 100%);
      color: #dc3545;
      border: 1.5px solid #f5bfc0;
    }
    .edit-modal-content input:focus, .edit-modal-content select:focus, .edit-modal-content textarea:focus {
      box-shadow: 0 0 0 2px #0dcaf0;
      border-color: #0dcaf0;
    }
    .edit-modal-content input[readonly] {
      cursor: not-allowed;
    }
    .edit-modal-content .form-control {
      font-size: 1.05rem;
      padding: 0.55rem 0.9rem;
    }
    .edit-modal-content .form-label {
      margin-bottom: 0.35rem;
    }
    .edit-modal-content .mb-3:last-child {
      margin-bottom: 0.7rem !important;
    }
    .edit-modal-content .modal-body {
      min-height: 180px;
    }
    .edit-modal-content .modal-header {
      min-height: 80px;
    }
    .edit-modal-content .modal-footer {
      min-height: 60px;
    }
    .edit-modal-content .spinner-border {
      vertical-align: middle;
    }
    /* --- Edit Modal Two-Box Layout --- */
    .edit-modal-flex {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
      width: 100%;
      animation: fadeInBoxes 0.5s cubic-bezier(.4,0,.2,1);
    }
    @keyframes fadeInBoxes {
      from { opacity: 0; transform: translateY(30px);}
      to { opacity: 1; transform: translateY(0);}
    }
    @media (min-width: 768px) {
      .edit-modal-flex {
        flex-direction: row;
        gap: 2.5rem;
      }
    }
    .edit-original-box, .edit-editable-box {
      flex: 1 1 0;
      min-width: 220px;
      max-width: 440px;
      background: #f8f9fa;
      border: 2px solid #e0e0e0;
      border-radius: 0;
      padding: 1.3rem 1.3rem 1.3rem 1.3rem;
      box-sizing: border-box;
      box-shadow: 0 2px 8px rgba(13,110,253,0.04);
      transition: box-shadow 0.2s, border-color 0.2s;
    }
    .edit-original-box {
      background: #f8f9fa;
      border: 2px solid #e0e0e0;
    }
    .edit-editable-box {
      background: #fff;
      border: 2px solid #0dcaf0;
      box-shadow: 0 4px 16px rgba(13,110,253,0.08);
    }
    .edit-box-title {
      font-weight: 700;
      font-size: 1.13rem;
      margin-bottom: 0.7rem;
      color: #0d6efd;
      letter-spacing: 0.01em;
      text-align: left;
    }
    .edit-section-title {
      font-weight: 500;
      font-size: 1.01rem;
      margin: 0.7rem 0 0.3rem 0;
      color: #495057;
      border-left: 3px solid #0dcaf0;
      padding-left: 0.5rem;
    }
    .edit-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      align-items: center;
      gap: 0.3rem 0.7rem;
      margin-bottom: 0.3rem;
      transition: background 0.2s;
    }
    .edit-label {
      font-size: 0.99rem;
      color: #6c757d;
      font-weight: 500;
      text-align: left;
      white-space: nowrap;
    }
    .edit-value {
      font-size: 1.05rem;
      color: #212529;
      font-weight: 600;
      text-align: right;
      white-space: nowrap;
    }
    .edit-input {
      width: 100%;
      font-size: 1.05rem;
      padding: 0.25rem 0.5rem;
      border-radius: 0.4rem;
      border: 1.5px solid #e0e0e0;
      background: #f8f9fa;
      color: #212529;
      font-weight: 500;
      transition: border-color 0.2s, background 0.2s;
    }
    .edit-input:focus {
      border-color: #0dcaf0;
      background: #e9f7fe;
    }
    .edit-form-fields-inner {
      display: grid;
      grid-template-columns: 1fr;
      gap: 0.5rem 1.2rem;
    }
    @media (min-width: 576px) {
      .edit-form-fields-inner {
        grid-template-columns: 1fr 1fr;
      }
    }
    /* Highlight changed fields */
    .edit-row.changed {
      background: linear-gradient(90deg, #e0f7fa 0%, #f8f9fa 100%);
      border-radius: 0.4rem;
      box-shadow: 0 1px 4px rgba(13,110,253,0.07);
    }
    /* Reset button animation */
    #editFormResetBtn:active {
      animation: btnPop 0.2s;
    }
    @keyframes btnPop {
      0% { transform: scale(1);}
      50% { transform: scale(1.08);}
      100% { transform: scale(1);}
    }
  </style>
  <script>
    // Highlight changed fields in the edit modal
    function highlightChangedFields() {
      $('.edit-modal-flex').each(function() {
        const $flex = $(this);
        $flex.find('.edit-row').each(function() {
          const $row = $(this);
          const $input = $row.find('.edit-input');
          if ($input.length) {
            const origVal = $row.data('original');
            const editVal = $input.val();
            if (String(origVal) !== String(editVal)) {
              $row.addClass('changed');
            } else {
              $row.removeClass('changed');
            }
          }
        });
      });
    }
    // Reset editable fields to original values
    $(document).on('click', '#editFormResetBtn', function() {
      $('#editFormFields .edit-row').each(function() {
        const $row = $(this);
        const $input = $row.find('.edit-input');
        if ($input.length) {
          $input.val($row.data('original'));
        }
      });
      highlightChangedFields();
    });
    // Live highlight on input
    $(document).on('input', '.edit-input', function() {
      highlightChangedFields();
    });
    // On modal open, highlight changed fields
    $(document).on('shown.bs.modal', '#editModal', function() {
      setTimeout(highlightChangedFields, 100);
    });

    // ==========================
    // Data Analysis Functionality
    // (Refactored for robustness, modularity, and Google-level polish)
    // ==========================
    (function() {
      'use strict';
      // Data Analysis functionality
      let analysisChart = null;
      let analysisData = null;

// Data Analysis button click handler
$('#dataAnalysisBtn').on('click', function() {
  // Set default start/end date in modal to yesterday
  const yesterday = moment().subtract(1, 'days').format('YYYY-MM-DD');
  $('#dataAnalysisStartDate').val(yesterday);
  $('#dataAnalysisEndDate').val(yesterday);
  openOverallDataAnalysisModal();
});

// Data Analysis modal filter button handler
$('#dataAnalysisFilterBtn').on('click', function() {
  openOverallDataAnalysisModal();
});

/**
 * Show overall (aggregate) data analysis for wind, solar, and both.
 * Handles modal display, loading state, AJAX fetching, error handling, and populates plant dropdown and chart data.
 * All user feedback is robust and accessible.
 */
function openOverallDataAnalysisModal() {
  $('#dataAnalysisModal').modal('show');
  $('#analysisLoading').show();
  $('#analysisContent').hide();
  $('#noDataMessage').hide();

  // Update modal title to reflect overall analysis
  $('#dataAnalysisModalLabel').html(
    `<i class="fas fa-chart-bar me-2"></i>Data Analysis (Overall) - Prescinto vs Edited Values`
  );

  // Fetch plant/date options and analysis data for all types
  const types = ['wind', 'solar', 'both'];
  // Use modal's date filters if present, else fallback to main dashboard
  const startDate = $('#dataAnalysisStartDate').val() || $('#startDateInput').val();
  const endDate = $('#dataAnalysisEndDate').val() || $('#endDateInput').val();

  // Fetch plant/date options for all types and merge
  const plantDatePromises = types.map(type =>
    $.ajax({
      url: '/api/plant-date-options',
      method: 'GET',
      data: { type }
    })
  );

  // Fetch analysis data for all types and merge
  const dataPromises = types.map(type =>
    $.ajax({
      url: '/api/data-analysis',
      method: 'GET',
      data: { type, start_date: startDate, end_date: endDate }
    })
  );

  // Wait for all AJAX calls to complete
  Promise.all([...plantDatePromises, ...dataPromises])
    .then(function(responses) {
      // Merge plant/date options
      const plantSets = [new Set(), new Set()];
      for (let i = 0; i < types.length; i++) {
        const resp = responses[i];
        if (resp.success) {
          resp.plants.forEach(p => plantSets[0].add(p));
          resp.dates.forEach(d => plantSets[1].add(d));
        }
      }
      const allPlants = Array.from(plantSets[0]).sort();
      const allDates = Array.from(plantSets[1]).sort();

      // Merge analysis data
      let allData = [];
      for (let i = 0; i < types.length; i++) {
        const resp = responses[i + types.length];
        if (resp.data && Array.isArray(resp.data)) {
          // Normalize each record to have a consistent plant_name and status
          const type = types[i];
          const normalized = resp.data.map(record => {
            let plant_name = '';
            if (type === 'wind') {
              plant_name = record.plant_long_name || record.plant_name || '';
            } else if (type === 'solar') {
              plant_name = record.plant_long_name || record.plant_name || '';
            } else if (type === 'both') {
              // For "both", combine solar and wind plant names for uniqueness
              plant_name = (record.plant_long_name_solar || '') + ' + ' + (record.plant_long_name_wind || '');
            }
            // Normalize status field
            let status = record.status || '';
            // Normalize date field
            let date = record.date || '';
            // Return normalized record with all original fields
            return { ...record, plant_name, status, date, _type: type };
          });
          allData = allData.concat(normalized);
        }
      }

      // Populate plant dropdown with type info
      const plantSelect = $('#analysisPlant');
      plantSelect.empty().append('<option value="all">All Plants</option>');
      // Build a map of plant name to type for later use
      window.analysisPlantTypeMap = {};
      for (let i = 0; i < types.length; i++) {
        const resp = responses[i];
        if (resp.success) {
          const type = types[i];
          resp.plants.forEach(p => {
            plantSelect.append(`<option value="${type}::${p}">${p} (${type.charAt(0).toUpperCase() + type.slice(1)})</option>`);
            window.analysisPlantTypeMap[p] = type;
          });
        }
      }

      $('#analysisLoading').hide();
      // Always show analysis content and 30-day trend, even if allData is empty
      analysisData = { data: allData };
      setupAnalysisControls(analysisData);
      $('#analysisContent').show();
      render30DayTrendChart(analysisData); // Show 30-day trend by default
      // Visually activate the 30-day trend button, deactivate others
      $('#show30DayTrendBtn').addClass('active');
      $('#showAllDatesChartBtn, #showPendingSentDiffBtn').removeClass('active');
      // Show "No data" message only if allData is empty, but do NOT hide the trend chart
      if (allData.length === 0) {
        $('#noDataMessage').show();
      } else {
        $('#noDataMessage').hide();
      }
    })
    .catch(function(error) {
      // Robust error handling and user feedback
      $('#analysisLoading').hide();
      $('#analysisContent').show();
      render30DayTrendChart({ data: [] });
      $('#show30DayTrendBtn').addClass('active');
      $('#showAllDatesChartBtn, #showPendingSentDiffBtn').removeClass('active');
      $('#noDataMessage').show().html('<h5 class="text-danger">Error loading data analysis. Please try again later.</h5>');
      // Log error for debugging
      if (window && window.console) {
        console.error('Error fetching overall analysis data:', error);
      }
    });
}

    function setupAnalysisControls(data) {
      const plantSelect = $('#analysisPlant');

      // Set up change handlers
      plantSelect.off('change').on('change', () => renderAnalysisChart(data));

      // Show all dates chart button handler
      $('#showAllDatesChartBtn').off('click').on('click', function() {
        renderAllDatesChart(data);
      });

      // Pending vs Sent Difference chart handler
      $('#showPendingSentDiffBtn').off('click').on('click', function() {
        renderPendingSentDiffChart(data);
      });

      // 30-Day Status Trend chart handler
      $('#show30DayTrendBtn').off('click').on('click', function() {
        render30DayTrendChart(data);
      });

      // Generation Diff Chart handler
      $('#showGenerationDiffBtn').off('click').on('click', function() {
        renderGenerationDiffChart();
      });
    }

    // Render 30-Day Status Trend line chart
    let trend30DayChart = null;
    function render30DayTrendChart(data) {
      // Hide other charts, show trend chart
      $('.chart-container').hide();
      $('#trend30DayChartContainer').show();

      const selectedPlant = $('#analysisPlant').val();

      // Show loading spinner in chart area
      $('#trend30DayChartContainer').append('<div id="trend30DayLoading" class="text-center py-4"><div class="spinner-border text-primary" role="status"></div><div>Loading trend...</div></div>');

      // Prepare query params
      let params = {};
      if (selectedPlant && selectedPlant !== 'all') {
        params.plant_name = selectedPlant;
      }

      $.ajax({
        url: '/api/30day-trend',
        method: 'GET',
        data: params,
        success: function(resp) {
          $('#trend30DayLoading').remove();
          if (!resp.success || !Array.isArray(resp.trend) || resp.trend.length === 0) {
            $('#trend30DayChartContainer').hide();
            $('#noDataMessage').show();
            $('#analysisStats').html('');
            return;
          }
          $('#noDataMessage').hide();
          $('#trend30DayChartContainer').show();

          const trend = resp.trend;
          const dateList = trend.map(d => d.date);
          const sentCounts = trend.map(d => d["Sent"]);
          const sentUpdatedCounts = trend.map(d => d["Sent Updated"]);
          const pendingCounts = trend.map(d => d["Pending"]);

          // Destroy existing chart
          if (trend30DayChart) {
            trend30DayChart.destroy();
          }

          // Check if all counts are zero (i.e., no data)
          const allZero = sentCounts.concat(sentUpdatedCounts, pendingCounts).every(v => v === 0);
          if (allZero) {
            $('#trend30DayChartContainer').hide();
            $('#noDataMessage').show();
            $('#analysisStats').html('');
            return;
          } else {
            $('#noDataMessage').hide();
            $('#trend30DayChartContainer').show();
          }

          const ctx = document.getElementById('trend30DayChart').getContext('2d');
          trend30DayChart = new Chart(ctx, {
            type: 'line',
            data: {
              labels: dateList,
              datasets: [
                {
                  label: 'Sent',
                  data: sentCounts,
                  borderColor: 'rgba(40, 167, 69, 1)',
                  backgroundColor: 'rgba(40, 167, 69, 0.1)',
                  fill: false,
                  tension: 0.2
                },
                {
                  label: 'Sent Updated',
                  data: sentUpdatedCounts,
                  borderColor: 'rgba(23, 162, 184, 1)',
                  backgroundColor: 'rgba(23, 162, 184, 0.1)',
                  fill: false,
                  tension: 0.2
                },
                {
                  label: 'Pending',
                  data: pendingCounts,
                  borderColor: 'rgba(255, 193, 7, 1)',
                  backgroundColor: 'rgba(255, 193, 7, 0.1)',
                  fill: false,
                  tension: 0.2
                }
              ]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                title: {
                  display: true,
                  text: 'Last 30 Days Trend: Sent, Sent Updated, Pending'
                },
                legend: {
                  position: 'top'
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  title: {
                    display: true,
                    text: 'Count'
                  }
                },
                x: {
                  title: {
                    display: true,
                    text: 'Date'
                  },
                  ticks: {
                    maxTicksLimit: 10,
                    autoSkip: true
                  }
                }
              }
            }
          });
          $('#analysisStats').html('');
        },
        error: function() {
          $('#trend30DayLoading').remove();
          $('#trend30DayChartContainer').hide();
          $('#noDataMessage').show();
          $('#analysisStats').html('');
        }
      });
    }

    // Render Pending vs Sent Difference chart
    let pendingSentDiffChart = null;
    function renderPendingSentDiffChart(data) {
      // Hide main chart, show diff chart
      $('.chart-container').hide();
      $('#pendingSentDiffChartContainer').show();

      const selectedPlant = $('#analysisPlant').val();
      // Use modal's date filters if present, else fallback to main dashboard
      const startDate = $('#dataAnalysisStartDate').val() || $('#startDateInput').val();
      const endDate = $('#dataAnalysisEndDate').val() || $('#endDateInput').val();

      let filteredData = data.data;
      if (selectedPlant !== 'all') {
        filteredData = filteredData.filter(record => record.plant_name === selectedPlant);
      }
      if (startDate) {
        filteredData = filteredData.filter(record => record.date >= startDate);
      }
      if (endDate) {
        filteredData = filteredData.filter(record => record.date <= endDate);
      }

      let sentCount = 0;
      let sentUpdatedCount = 0;
      let pendingCount = 0;
      filteredData.forEach(record => {
        if (record.status === "Sent") sentCount += 1;
        else if (record.status === "Sent Updated") sentUpdatedCount += 1;
        else if (record.status === "Pending") pendingCount += 1;
      });

      // Destroy existing diff chart
      if (pendingSentDiffChart) {
        pendingSentDiffChart.destroy();
      }

      const ctx = document.getElementById('pendingSentDiffChart').getContext('2d');
      pendingSentDiffChart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: ['Pending', 'Sent', 'Sent Updated'],
          datasets: [{
            label: 'Count',
            data: [pendingCount, sentCount, sentUpdatedCount],
            backgroundColor: [
              'rgba(255, 193, 7, 0.7)',    // Pending - yellow
              'rgba(40, 167, 69, 0.7)',    // Sent - green
              'rgba(23, 162, 184, 0.7)'    // Sent Updated - blue/cyan
            ],
            borderColor: [
              'rgba(255, 193, 7, 1)',
              'rgba(40, 167, 69, 1)',
              'rgba(23, 162, 184, 1)'
            ],
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            title: {
              display: true,
              text: 'Pending vs Sent vs Sent Updated'
            },
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: 'Count'
              }
            }
          }
        }
      });
      $('#analysisStats').html(
        `<div class="alert alert-info mt-3">Sent: <b>${sentCount}</b>, Sent Updated: <b>${sentUpdatedCount}</b>, Pending: <b>${pendingCount}</b></div>`
      );
    }

    function renderAnalysisChart(data) {
      // Show main chart, hide diff chart
      $('.chart-container').hide();
      $('.chart-container').first().show();
      $('#pendingSentDiffChartContainer').hide();

      const selectedPlant = $('#analysisPlant').val();
      // Use modal's date filters if present, else fallback to main dashboard
      const startDate = $('#dataAnalysisStartDate').val() || $('#startDateInput').val();
      const endDate = $('#dataAnalysisEndDate').val() || $('#endDateInput').val();

      let filteredData = data.data;
      if (selectedPlant !== 'all') {
        filteredData = filteredData.filter(record => record.plant_name === selectedPlant);
      }
      // Filter by modal date range
      if (startDate) {
        filteredData = filteredData.filter(record => record.date >= startDate);
      }
      if (endDate) {
        filteredData = filteredData.filter(record => record.date <= endDate);
      }

      // Count records by status: "Sent" (Prescinto) and "Sent Updated" (Edited)
      let prescintoCount = 0;
      let editedCount = 0;
      filteredData.forEach(record => {
        if (record.status === "Sent Updated") {
          editedCount += 1;
        } else if (record.status === "Sent") {
          prescintoCount += 1;
        }
      });

      // Destroy existing chart
      if (analysisChart) {
        analysisChart.destroy();
      }

      const ctx = document.getElementById('analysisChart').getContext('2d');
      analysisChart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: ['Prescinto', 'Edited'],
          datasets: [{
            label: 'Count',
            data: [prescintoCount, editedCount],
            backgroundColor: ['rgba(54, 162, 235, 0.7)', 'rgba(255, 99, 132, 0.7)'],
            borderColor: ['rgba(54, 162, 235, 1)', 'rgba(255, 99, 132, 1)'],
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            title: {
              display: true,
              text: 'Prescinto vs Edited Record Count'
            },
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: 'Count'
              }
            },
            x: {
              title: {
                display: false
              }
            }
          }
        }
      });
      $('#analysisStats').html('');
    }

    // Show all dates chart (for selected plant, all metrics averaged)
function renderAllDatesChart(data) {
      // Show main chart, hide other charts
      $('.chart-container').hide();
      $('.chart-container').first().show();
      $('#pendingSentDiffChartContainer').hide();
      $('#trend30DayChartContainer').hide();

      const selectedPlant = $('#analysisPlant').val();
      // Use modal's date filters if present, else fallback to main dashboard
      const startDate = $('#dataAnalysisStartDate').val() || $('#startDateInput').val();
      const endDate = $('#dataAnalysisEndDate').val() || $('#endDateInput').val();

      let filteredData = data.data;
      if (selectedPlant !== 'all') {
        filteredData = filteredData.filter(record => record.plant_name === selectedPlant);
      }
      // Filter by modal date range
      if (startDate) {
        filteredData = filteredData.filter(record => record.date >= startDate);
      }
      if (endDate) {
        filteredData = filteredData.filter(record => record.date <= endDate);
      }

      // Group by date, count "Sent" (Prescinto) and "Sent Updated" (Edited) per date
      const dateMap = {};
      filteredData.forEach(record => {
        if (!dateMap[record.date]) dateMap[record.date] = { prescinto: 0, edited: 0 };
        if (record.status === "Sent Updated") {
          dateMap[record.date].edited += 1;
        } else if (record.status === "Sent") {
          dateMap[record.date].prescinto += 1;
        }
      });

      const dates = Object.keys(dateMap).sort();
      const prescintoCounts = dates.map(date => dateMap[date].prescinto);
      const editedCounts = dates.map(date => dateMap[date].edited);

      // Destroy existing chart
      if (analysisChart) {
        analysisChart.destroy();
      }

      const ctx = document.getElementById('analysisChart').getContext('2d');
      analysisChart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: dates,
          datasets: [
            {
              label: 'Prescinto',
              data: prescintoCounts,
              backgroundColor: 'rgba(54, 162, 235, 0.7)',
              borderColor: 'rgba(54, 162, 235, 1)',
              borderWidth: 1
            },
            {
              label: 'Edited',
              data: editedCounts,
              backgroundColor: 'rgba(255, 99, 132, 0.7)',
              borderColor: 'rgba(255, 99, 132, 1)',
              borderWidth: 1
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            title: {
              display: true,
text: 'Prescinto vs Edited Count by Date'
            },
            legend: {
              position: 'top'
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: 'Count'
              }
            },
            x: {
              title: {
                display: true,
                text: 'Date'
              }
            }
          }
        }
      });
      $('#analysisStats').html('');
    }

    function updateAnalysisStats(data, metric) {
      const originalValues = data.map(record => record.metrics[metric].original);
      const editedValues = data.map(record => record.metrics[metric].edited);
      
      const avgOriginal = originalValues.reduce((a, b) => a + b, 0) / originalValues.length;
      const avgEdited = editedValues.reduce((a, b) => a + b, 0) / editedValues.length;
      const avgDifference = avgEdited - avgOriginal;
      const percentChange = ((avgDifference / avgOriginal) * 100).toFixed(2);
      
      const statsHtml = `
        <div class="row">
          <div class="col-md-3">
            <div class="card bg-light">
              <div class="card-body text-center">
                <h6 class="card-title">Records Analyzed</h6>
                <h4 class="text-primary">${data.length}</h4>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card bg-light">
              <div class="card-body text-center">
<h6 class="card-title">Avg Prescinto</h6>
                <h4 class="text-info">${avgOriginal.toFixed(2)}</h4>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card bg-light">
              <div class="card-body text-center">
                <h6 class="card-title">Avg Edited</h6>
                <h4 class="text-warning">${avgEdited.toFixed(2)}</h4>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="card bg-light">
              <div class="card-body text-center">
                <h6 class="card-title">Change %</h6>
                <h4 class="${percentChange >= 0 ? 'text-success' : 'text-danger'}">${percentChange}%</h4>
              </div>
            </div>
          </div>
        </div>
      `;
      
      $('#analysisStats').html(statsHtml);
    }

    // Export chart functionality
    $('#exportAnalysisBtn').on('click', function() {
      // Prefer exporting the visible chart
      let chartToExport = null;
      if ($('#generationDiffChartContainer').is(':visible') && generationDiffChart) {
        chartToExport = generationDiffChart;
      } else if ($('#analysisChart').is(':visible') && analysisChart) {
        chartToExport = analysisChart;
      } else if ($('#pendingSentDiffChartContainer').is(':visible') && pendingSentDiffChart) {
        chartToExport = pendingSentDiffChart;
      } else if ($('#trend30DayChartContainer').is(':visible') && trend30DayChart) {
        chartToExport = trend30DayChart;
      }
      if (chartToExport) {
        const link = document.createElement('a');
        link.download = 'data-analysis-chart.png';
        link.href = chartToExport.toBase64Image();
        link.click();
      }
    });

    // Clean up chart when modal is hidden
    $('#dataAnalysisModal').on('hidden.bs.modal', function() {
      if (analysisChart) {
        analysisChart.destroy();
        analysisChart = null;
      }
      if (generationDiffChart) {
        generationDiffChart.destroy();
        generationDiffChart = null;
      }
    });

    // --- Generation Diff Chart Logic ---
    let generationDiffChart = null;
    function renderGenerationDiffChart() {
      // Hide other charts, show generation diff chart
      $('.chart-container').hide();
      $('#generationDiffChartContainer').show();

      const selectedPlantVal = $('#analysisPlant').val();
      const startDate = $('#dataAnalysisStartDate').val() || $('#startDateInput').val();
      const endDate = $('#dataAnalysisEndDate').val() || $('#endDateInput').val();

      // If plant is "all", show message and return
      if (!selectedPlantVal || selectedPlantVal === 'all') {
        $('#generationDiffChartContainer').hide();
        $('#noDataMessage').show().html('<h5>Please select a specific plant to view generation diff.</h5>');
        return;
      }

      // Parse type and plant name from value
      let type = 'wind';
      let plantName = '';
      if (selectedPlantVal.includes('::')) {
        [type, plantName] = selectedPlantVal.split('::');
      } else {
        plantName = selectedPlantVal;
        // fallback: try to get type from map
        if (window.analysisPlantTypeMap && window.analysisPlantTypeMap[plantName]) {
          type = window.analysisPlantTypeMap[plantName];
        }
      }

      // Show loading spinner
      $('#generationDiffChartContainer').append('<div id="generationDiffLoading" class="text-center py-4"><div class="spinner-border text-danger" role="status"></div><div>Loading generation diff...</div></div>');

      // Fetch data from backend
      $.ajax({
        url: '/api/generation-diff',
        method: 'GET',
        data: {
          type: type,
          plant_name: plantName,
          start_date: startDate,
          end_date: endDate
        },
        success: function(resp) {
          $('#generationDiffLoading').remove();
          // Debug: print all returned data and selection context
          console.log("GEN DIFF DEBUG: type:", type, "plantName:", plantName);
          if (resp && Array.isArray(resp.data)) {
            console.log("GEN DIFF DEBUG: resp.data.length:", resp.data.length);
            if (resp.data.length > 0) {
              console.log("GEN DIFF DEBUG: resp.data[0]:", resp.data[0]);
              console.log("GEN DIFF DEBUG: resp.data[1]:", resp.data[1]);
            }
          } else {
            console.log("GEN DIFF DEBUG: resp.data is not an array or missing");
          }
          if (!resp.success || !Array.isArray(resp.data) || resp.data.length === 0) {
            $('#generationDiffChartContainer').hide();
            $('#generationDiffChartContainerSolar').hide();
            $('#generationDiffChartContainerWind').hide();
            $('#noDataMessage').show().html('<h5>No generation data found for this plant and date range.</h5>');
            return;
          }
          $('#noDataMessage').hide();
          $('#generationDiffChartContainer').hide();
          $('#generationDiffChartContainerSolar').hide();
          $('#generationDiffChartContainerWind').hide();

          // Prepare data for chart
          let chartData = [];
          if (type === "both") {
            // For "both", check if the selected plant matches either solar or wind short name
            // Robust matching for plant selection
            function matchesPlant(row, plantName) {
              if (!row || !plantName) return false;
              // Normalize for case-insensitive comparison
              const pn = plantName.trim().toLowerCase();
              return (
                (row.plant_short_name_solar && row.plant_short_name_solar.trim().toLowerCase() === pn) ||
                (row.plant_short_name_wind && row.plant_short_name_wind.trim().toLowerCase() === pn) ||
                (row.plant_long_name_solar && row.plant_long_name_solar.trim().toLowerCase() === pn) ||
                (row.plant_long_name_wind && row.plant_long_name_wind.trim().toLowerCase() === pn) ||
                (row.plant_long_name_solar && row.plant_long_name_wind &&
                  (row.plant_long_name_solar + ' + ' + row.plant_long_name_wind).trim().toLowerCase() === pn)
              );
            }
            const plantNameLower = plantName.trim().toLowerCase();
            // Backend now uses plant_name and subtype fields
            const solarRows = resp.data.filter(row =>
              row.plant_name && row.plant_name.trim().toLowerCase() === plantNameLower &&
              row.subtype && row.subtype.toLowerCase() === 'solar'
            );
            // For wind, show all wind rows for the date range (for "both" type)
            const windRows = resp.data.filter(row =>
              row.subtype && row.subtype.toLowerCase() === 'wind'
            );

            // Debug logs to help diagnose why plot is not showing
            console.log("GEN DIFF DEBUG: type:", type, "plantName:", plantName);
            console.log("GEN DIFF DEBUG: solarRows.length:", solarRows.length, "windRows.length:", windRows.length);
            if (solarRows.length > 0) {
              console.log("GEN DIFF DEBUG: solarRows[0]:", solarRows[0]);
            }
            if (windRows.length > 0) {
              console.log("GEN DIFF DEBUG: windRows[0]:", windRows[0]);
            }
            // Print all plant_short_name_solar and plant_short_name_wind in resp.data for reference
            console.log("GEN DIFF DEBUG: all plant_short_name_solar in resp.data:", resp.data.map(r => r.plant_short_name_solar));
            console.log("GEN DIFF DEBUG: all plant_short_name_wind in resp.data:", resp.data.map(r => r.plant_short_name_wind));
            console.log("GEN DIFF DEBUG: all plant_long_name_solar in resp.data:", resp.data.map(r => r.plant_long_name_solar));
            console.log("GEN DIFF DEBUG: all plant_long_name_wind in resp.data:", resp.data.map(r => r.plant_long_name_wind));

            // Show both charts if both solar and wind data exist
            let anyChart = false;
            // Always hide all containers first
            $('#generationDiffChartContainer').hide();
            $('#generationDiffChartContainerSolar').hide();
            $('#generationDiffChartContainerWind').hide();

            if (solarRows.length > 0) {
              $('#generationDiffChartContainerSolar').show();
              if (window.generationDiffChartSolar && typeof window.generationDiffChartSolar.destroy === "function") {
                window.generationDiffChartSolar.destroy();
              }
              const dateMapSolar = {};
              solarRows.forEach(row => {
                if (!dateMapSolar[row.date]) dateMapSolar[row.date] = { original: null, edited: null };
                if (row.original_generation !== null && row.original_generation !== undefined && dateMapSolar[row.date].original === null) {
                  dateMapSolar[row.date].original = row.original_generation;
                }
                if (row.edited_generation !== null && row.edited_generation !== undefined && dateMapSolar[row.date].edited === null) {
                  dateMapSolar[row.date].edited = row.edited_generation;
                }
              });
              const datesSolar = Object.keys(dateMapSolar).sort();
              const originalValsSolar = datesSolar.map(date => dateMapSolar[date].original);
              const editedValsSolar = datesSolar.map(date => dateMapSolar[date].edited);
              const ctxSolar = document.getElementById('generationDiffChartSolar').getContext('2d');
              window.generationDiffChartSolar = new Chart(ctxSolar, {
                type: 'bar',
                data: {
                  labels: datesSolar,
                  datasets: [
                    {
                      label: 'Original Generation (Solar)',
                      data: originalValsSolar,
                      backgroundColor: 'rgba(255, 206, 86, 0.7)',
                      borderColor: 'rgba(255, 206, 86, 1)',
                      borderWidth: 1
                    },
                    {
                      label: 'Edited Generation (Solar)',
                      data: editedValsSolar,
                      backgroundColor: 'rgba(255, 99, 132, 0.7)',
                      borderColor: 'rgba(255, 99, 132, 1)',
                      borderWidth: 1
                    }
                  ]
                },
                options: {
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    title: {
                      display: true,
                      text: 'Original vs Edited Generation by Date (Solar)'
                    },
                    legend: { position: 'top' }
                  },
                  scales: {
                    y: { beginAtZero: true, title: { display: true, text: 'Generation (kWh)' } },
                    x: { title: { display: true, text: 'Date' } }
                  }
                }
              });
              anyChart = true;
            }
            if (windRows.length > 0) {
              $('#generationDiffChartContainerWind').show();
              if (window.generationDiffChartWind && typeof window.generationDiffChartWind.destroy === "function") {
                window.generationDiffChartWind.destroy();
              }
              const dateMapWind = {};
              windRows.forEach(row => {
                if (!dateMapWind[row.date]) dateMapWind[row.date] = { original: null, edited: null };
                if (row.original_generation !== null && row.original_generation !== undefined && dateMapWind[row.date].original === null) {
                  dateMapWind[row.date].original = row.original_generation;
                }
                if (row.edited_generation !== null && row.edited_generation !== undefined && dateMapWind[row.date].edited === null) {
                  dateMapWind[row.date].edited = row.edited_generation;
                }
              });
              const datesWind = Object.keys(dateMapWind).sort();
              const originalValsWind = datesWind.map(date => dateMapWind[date].original);
              const editedValsWind = datesWind.map(date => dateMapWind[date].edited);
              const ctxWind = document.getElementById('generationDiffChartWind').getContext('2d');
              window.generationDiffChartWind = new Chart(ctxWind, {
                type: 'bar',
                data: {
                  labels: datesWind,
                  datasets: [
                    {
                      label: 'Original Generation (Wind)',
                      data: originalValsWind,
                      backgroundColor: 'rgba(54, 162, 235, 0.7)',
                      borderColor: 'rgba(54, 162, 235, 1)',
                      borderWidth: 1
                    },
                    {
                      label: 'Edited Generation (Wind)',
                      data: editedValsWind,
                      backgroundColor: 'rgba(75, 192, 192, 0.7)',
                      borderColor: 'rgba(75, 192, 192, 1)',
                      borderWidth: 1
                    }
                  ]
                },
                options: {
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    title: {
                      display: true,
                      text: 'Original vs Edited Generation by Date (Wind)'
                    },
                    legend: { position: 'top' }
                  },
                  scales: {
                    y: { beginAtZero: true, title: { display: true, text: 'Generation (kWh)' } },
                    x: { title: { display: true, text: 'Date' } }
                  }
                }
              });
              anyChart = true;
            }
            if (anyChart) {
              $('#analysisStats').html('');
              return;
            }
            // If neither, fallback to single chart logic below
            chartData = [];
            $('#generationDiffChartContainer').hide();
            $('#generationDiffChartContainerSolar').hide();
            $('#generationDiffChartContainerWind').hide();
            $('#noDataMessage').show().html('<h5>No generation data found for this plant and date range.</h5>');
            return;
          } else {
            chartData = resp.data.filter(row => row.plant_name === plantName);
            $('#generationDiffChartContainer').show();
          }

          // Group by date, aggregate all rows for each date
          const dateMap = {};
          chartData.forEach(row => {
            if (!dateMap[row.date]) {
              dateMap[row.date] = { original: null, edited: null };
            }
            // Take the first non-null value for each
            if (row.original_generation !== null && row.original_generation !== undefined && dateMap[row.date].original === null) {
              dateMap[row.date].original = row.original_generation;
            }
            if (row.edited_generation !== null && row.edited_generation !== undefined && dateMap[row.date].edited === null) {
              dateMap[row.date].edited = row.edited_generation;
            }
          });
          const dates = Object.keys(dateMap).sort();
          const originalVals = dates.map(date => dateMap[date].original);
          const editedVals = dates.map(date => dateMap[date].edited);

          // Destroy existing chart
          if (generationDiffChart) {
            generationDiffChart.destroy();
          }

          const ctx = document.getElementById('generationDiffChart').getContext('2d');
          generationDiffChart = new Chart(ctx, {
            type: 'bar',
            data: {
              labels: dates,
              datasets: [
                {
                  label: 'Original Generation',
                  data: originalVals,
                  backgroundColor: 'rgba(54, 162, 235, 0.7)',
                  borderColor: 'rgba(54, 162, 235, 1)',
                  borderWidth: 1
                },
                {
                  label: 'Edited Generation',
                  data: editedVals,
                  backgroundColor: 'rgba(255, 99, 132, 0.7)',
                  borderColor: 'rgba(255, 99, 132, 1)',
                  borderWidth: 1
                }
              ]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                title: {
                  display: true,
                  text: 'Original vs Edited Generation by Date'
                },
                legend: {
                  position: 'top'
                }
              },
              scales: {
                y: {
                  beginAtZero: true,
                  title: {
                    display: true,
                    text: 'Generation (kWh)'
                  }
                },
                x: {
                  title: {
                    display: true,
                    text: 'Date'
                  }
                }
              }
            }
          });
          // Optionally, show stats
          let statsHtml = '';
          if (dates.length > 0) {
            const avgOrig = originalVals.reduce((a, b) => a + (b || 0), 0) / originalVals.length;
            const avgEdit = editedVals.reduce((a, b) => a + (b || 0), 0) / editedVals.length;
            const diff = avgEdit - avgOrig;
            const pct = avgOrig ? ((diff / avgOrig) * 100).toFixed(2) : '0.00';
            statsHtml = `
              <div class="alert alert-secondary mt-3">
                <b>Avg Original:</b> ${avgOrig.toFixed(2)} &nbsp; 
                <b>Avg Edited:</b> ${avgEdit.toFixed(2)} &nbsp; 
                <b>Change:</b> ${diff.toFixed(2)} (${pct}%)
              </div>
            `;
          }
          $('#analysisStats').html(statsHtml);
        },
        error: function() {
          $('#generationDiffLoading').remove();
          $('#generationDiffChartContainer').hide();
          $('#noDataMessage').show().html('<h5>Error loading generation diff data.</h5>');
        }
      });
    }

    // --- End Data Analysis Functionality ---
    })();
    // --- Report Status Modal Logic --- 
    function fetchAndRenderReportStatusTable() {
      var clientName = $('#filterClientName').val();
      var plantId = $('#filterPlantId').val();
      var status = $('#filterStatus').val();
      var startDate = $('#reportStatusStartDate').val();
      var endDate = $('#reportStatusEndDate').val();
      var $tbody = $('#reportStatusTableBody');
      // Debug: log filter values
      console.log('Report Status Filters:', {
        client_name: clientName,
        plant_id: plantId,
        status: status,
        start_date: startDate,
        end_date: endDate
      });
      $tbody.html('<tr><td colspan="5" class="text-center text-muted py-4"><div class="spinner-border spinner-border-sm me-2" role="status"></div>Loading...</td></tr>');
      $.ajax({
        url: '/api/report-status-data',
        method: 'GET',
        data: {
          client_name: clientName,
          plant_id: plantId,
          status: status,
          start_date: startDate,
          end_date: endDate
        },
        success: function(resp) {
          $tbody.empty();
          if (resp.success && resp.data.length > 0) {
            resp.data.forEach(function(row) {
              $tbody.append(
                '<tr>' +
                  '<td>' + (row.report_date || '') + '</td>' +
                  '<td>' + (row.plant || '') + '</td>' +
                  '<td>' + (row.recipient_id || '') + '</td>' +
                  '<td>' + (row.contact_person || '') + '</td>' +
                  '<td>' + (row.status || '') + '</td>' +
                '</tr>'
              );
            });
          } else {
            $tbody.html('<tr><td colspan="5" class="text-center text-muted py-4">No data found.</td></tr>');
          }
        },
        error: function() {
          $tbody.html('<tr><td colspan="5" class="text-center text-danger py-4">Error loading data.</td></tr>');
        }
      });
    }

    // --- Report Status Summary Cards ---
    // Status icon/color mapping (same as dashboard)
    // Status icon/color mapping for lowercase statuses
    const reportStatusIcons = {
      'sent': 'fa-check-circle',
      'delivered': 'fa-paper-plane',
      'read': 'fa-eye',
      'failed': 'fa-ban'
    };
    const reportStatusColors = {
      'sent': 'success',
      'delivered': 'info',
      'read': 'primary',
      'failed': 'danger'
    };
    // For unknown statuses, use info icon and primary color
    const unknownStatusIcon = 'fa-info-circle';
    const unknownStatusColor = 'primary';

    function renderReportStatusSummary(counts) {
      const $summary = $('#reportStatusSummary');
      $summary.empty();
      // Mapping for display labels (capitalize)
      const statusDisplayMap = {
        'sent': 'Sent',
        'delivered': 'Delivered',
        'read': 'Read',
        'failed': 'Failed'
      };
      // Only show these four statuses, in this order
      const orderedStatuses = ['sent', 'delivered', 'read', 'failed'];
      orderedStatuses.forEach(status => {
        // Try both lowercase and capitalized keys for compatibility
        const count = counts[status] || counts[status.charAt(0).toUpperCase() + status.slice(1)] || 0;
        const color = reportStatusColors[status] || unknownStatusColor;
        const icon = reportStatusIcons[status] || unknownStatusIcon;
        const displayLabel = statusDisplayMap[status] || status;
        $summary.append(`
          <div class="col-6 col-sm-4 col-md-3 col-lg">
            <div class="status-card border-${color} status-card-popup" style="background: linear-gradient(90deg, var(--light-gray) 70%, var(--${color}-color, #0d6efd) 100%);">
              <i class="fas ${icon} status-icon text-${color}"></i>
              <div>
                <div class="status-text" data-status="${status}">${displayLabel}</div>
                <div class="status-count text-${color}">${count}</div>
              </div>
            </div>
          </div>
        `);
      });
    }

    function fetchAndRenderReportStatusSummary() {
      // Get current filter values
      var clientName = $('#filterClientName').val();
      var plantId = $('#filterPlantId').val();
      var status = $('#filterStatus').val();
      var startDate = $('#reportStatusStartDate').val();
      var endDate = $('#reportStatusEndDate').val();
      $.ajax({
        url: '/api/report-status-counts',
        method: 'GET',
        data: {
          client_name: clientName,
          plant_id: plantId,
          status: status,
          start_date: startDate,
          end_date: endDate
        },
        success: function(resp) {
          if (resp.success && resp.counts) {
            renderReportStatusSummary(resp.counts);
          } else {
            $('#reportStatusSummary').empty();
          }
        },
        error: function() {
          $('#reportStatusSummary').empty();
        }
      });
    }

    // Report Status Button: Open Modal
    $('#reportStatusBtn').on('click', function() {
      // Hide main date filter row
      $('.row.g-3.align-items-end.mb-4').hide();

      // Set modal date filters to yesterday by default
      const yesterday = moment().subtract(1, 'days').format('YYYY-MM-DD');
      $('#reportStatusStartDate').val(yesterday);
      $('#reportStatusEndDate').val(yesterday);

      // Fetch filter options for client_name and plant_id
      $.ajax({
        url: '/api/report-status-options',
        method: 'GET',
        success: function(resp) {
          if (resp.success) {
            // Populate client_name dropdown
            var $client = $('#filterClientName');
            $client.empty().append('<option value="">All</option>');
            resp.client_names.forEach(function(name) {
              $client.append($('<option>').val(name).text(name));
            });
            // Populate plant_id dropdown
            var $plant = $('#filterPlantId');
            $plant.empty().append('<option value="">All</option>');
            resp.plant_ids.forEach(function(id) {
              $plant.append($('<option>').val(id).text(id));
            });
          }
          // Populate status dropdown from backend
          $.ajax({
            url: '/api/report-status-unique-statuses',
            method: 'GET',
            success: function(statusResp) {
              var $status = $('#filterStatus');
              $status.empty().append('<option value="">All</option>');
              if (statusResp.success && Array.isArray(statusResp.statuses)) {
                statusResp.statuses.forEach(function(s) {
                  $status.append($('<option>').val(s).text(s));
                });
              }
            }
          });
          // Re-attach change event handlers after populating dropdowns
          $('#filterClientName, #filterPlantId, #filterStatus').off('change').on('change', function() {
            fetchAndRenderReportStatusTable();
            fetchAndRenderReportStatusSummary();
          });
          // Re-attach filter button click handler every time modal is shown
          $('#reportStatusFilterBtn').off('click').on('click', function() {
            fetchAndRenderReportStatusTable();
            fetchAndRenderReportStatusSummary();
          });
          // After populating filters, fetch table data and summary
          fetchAndRenderReportStatusTable();
          fetchAndRenderReportStatusSummary();
        }
      });
      $('#reportStatusModal').modal('show');
    });

    // Show main date filter row when modal is closed
    $('#reportStatusModal').on('hidden.bs.modal', function() {
      $('.row.g-3.align-items-end.mb-4').show();
    });

    // Modal filter button click handler
    $('#reportStatusFilterBtn').on('click', function() {
      fetchAndRenderReportStatusTable();
      fetchAndRenderReportStatusSummary();
    });

    // Also trigger filter on Enter key in date fields
    $('#reportStatusStartDate, #reportStatusEndDate').on('keypress', function(e) {
      if (e.which === 13) {
        $('#reportStatusFilterBtn').trigger('click');
      }
    });

    // Fetch table data and summary when any filter changes
    $('#filterClientName, #filterPlantId, #filterStatus').on('change', function() {
      fetchAndRenderReportStatusTable();
      fetchAndRenderReportStatusSummary();
    });

    // Fetch table data when any filter changes
    $('#filterClientName, #filterPlantId, #filterStatus').on('change', function() {
      fetchAndRenderReportStatusTable();
    });
  </script>
  <!-- Alarms Data Modal -->
  <div class="modal fade" id="alarmsDataModal" tabindex="-1" aria-labelledby="alarmsDataModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header bg-danger text-white">
          <h5 class="modal-title" id="alarmsDataModalLabel">
            <i class="fas fa-bell me-2"></i>Alarms Data
          </h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body" id="alarmsDataModalBody">
          <!-- Form will be loaded here -->
        </div>
      </div>
    </div>
  </div>
  <script>
    // Open Alarms Data Modal and load form
    document.addEventListener('DOMContentLoaded', function() {
      document.getElementById('alarmsDataBtn').addEventListener('click', function() {
        var modalBody = document.getElementById('alarmsDataModalBody');
        modalBody.innerHTML = '<div class="text-center py-4"><div class="spinner-border text-danger" role="status"></div><div>Loading...</div></div>';
        fetch('/alarms_data_popup')
          .then(function(response) { return response.text(); })
          .then(function(html) {
            modalBody.innerHTML = html;
            // Provide a closeModal function for the cancel button
            window.closeModal = function() {
              var modal = bootstrap.Modal.getInstance(document.getElementById('alarmsDataModal'));
              if (modal) modal.hide();
            };
          })
          .catch(function() {
            modalBody.innerHTML = '<div class="text-danger text-center">Failed to load form.</div>';
          });
        var modal = new bootstrap.Modal(document.getElementById('alarmsDataModal'));
        modal.show();
      });
    });
  </script>
  <!-- Report Status Modal -->
  <div class="modal fade" id="reportStatusModal" tabindex="-1" aria-labelledby="reportStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header bg-primary text-white">
          <h5 class="modal-title" id="reportStatusModalLabel">
            <i class="fas fa-flag me-2"></i>Report Status
          </h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <!-- Status Summary Cards -->
          <div id="reportStatusSummary" class="row g-2 mb-3"></div>
          <!-- Filters -->
<form class="row g-3 mb-3 align-items-end" id="reportStatusFilters">
  <div class="col-md-2">
    <label for="reportStatusStartDate" class="form-label">Start Date</label>
    <input type="date" id="reportStatusStartDate" class="form-control" />
  </div>
  <div class="col-md-2">
    <label for="reportStatusEndDate" class="form-label">End Date</label>
    <input type="date" id="reportStatusEndDate" class="form-control" />
  </div>
  <div class="col-md-2">
    <button type="button" id="reportStatusFilterBtn" class="btn btn-primary w-100" style="margin-top: 1.95rem;">
      <i class="fas fa-filter me-1"></i>Filter
    </button>
  </div>
  <div class="col-md-2">
    <label for="filterClientName" class="form-label">Client Name</label>
    <select id="filterClientName" class="form-select">
      <option value="">All</option>
      <!-- Options will be populated dynamically -->
    </select>
  </div>
  <div class="col-md-2">
    <label for="filterPlantId" class="form-label">Plant ID</label>
    <select id="filterPlantId" class="form-select">
      <option value="">All</option>
      <!-- Options will be populated dynamically -->
    </select>
  </div>
  <div class="col-md-2">
    <label for="filterStatus" class="form-label">Status</label>
    <select id="filterStatus" class="form-select">
      <option value="">All</option>
      <!-- Options will be populated dynamically -->
    </select>
  </div>
</form>
          <!-- Table -->
          <div class="table-responsive">
            <table class="table table-hover align-middle">
              <thead>
                <tr>
                  <th>Report Date</th>
                  <th>Plant</th>
                  <th>Recipient ID</th>
                  <th>Contact Person</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody id="reportStatusTableBody">
                <!-- Data will be populated dynamically -->
              </tbody>
            </table>
          </div>
        </div>
        <div class="modal-footer bg-light">
<button type="button" class="btn btn-primary" id="exportReportStatusCsvBtn">
  <i class="fas fa-download me-1"></i>Export as CSV
</button>
<script>
  // Export Report Status Table as CSV
  document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('exportReportStatusCsvBtn').addEventListener('click', function() {
      var table = document.querySelector('#reportStatusTableBody').closest('table');
      var rows = Array.from(table.querySelectorAll('tr'));
      if (rows.length === 0) {
        alert('No data to export.');
        return;
      }
      // Get headers
      var headers = Array.from(table.querySelectorAll('thead th')).map(th => th.innerText.trim());
      var csv = [];
      csv.push(headers.join(','));
      // Get rows
      rows.forEach(function(row) {
        var cols = Array.from(row.querySelectorAll('td')).map(td => {
          // Escape double quotes and commas
          var text = td.innerText.replace(/"/g, '""');
          if (text.indexOf(',') !== -1 || text.indexOf('"') !== -1) {
            text = '"' + text + '"';
          }
          return text;
        });
        if (cols.length) csv.push(cols.join(','));
      });
      var csvContent = csv.join('\r\n');
      var blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      var link = document.createElement('a');
      var url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', 'report_status.csv');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    });
  });
</script>
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
