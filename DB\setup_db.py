from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from DB.models import Base
import os
from dotenv import load_dotenv
load_dotenv()

# Get the database URL from environment variable or use default
DB_URL = os.getenv('DB_URL')

if not DB_URL:
    raise ValueError("DB_URL environment variable is not set. Please check your .env file.")

try:
    engine = create_engine(DB_URL, pool_pre_ping=True)
except Exception as e:
    raise ValueError(f"Failed to create database engine. Check your DB_URL format: {DB_URL}. Error: {e}")
Base.metadata.bind = engine


# # ✅ Create tables if they don't exist
Base.metadata.create_all(engine)

# ✅ Use scoped_session here
session_factory = sessionmaker(bind=engine)
session = scoped_session(session_factory)

