# Database Schema Definition

This document describes the database schema, including tables, columns, types, and descriptions for each field.

---

## Table: `whatsapp_messages`

| Column Name      | Type         | Description                                      |
|------------------|--------------|--------------------------------------------------|
| id               | Integer (PK) | Unique identifier for the message                |
| wa_id            | String(20)   | WhatsApp user ID                                 |
| message_id       | String(100)  | Unique message identifier                        |
| report_type      | String(50)   | Type of report associated with the message       |
| plant_short_name | String(100)  | Short name of the plant                          |
| plant_long_name  | String(100)  | Long name of the plant                           |
| date             | Date         | Date of the message/report                       |
| dgr_path         | String(500)  | Path to the DGR report file                      |

---

## Table: `dgr_both_db`

| Column Name                  | Type         | Description                                                      |
|------------------------------|--------------|------------------------------------------------------------------|
| id                           | Integer (PK) | Unique identifier                                                |
| date                         | Date         | Date of the report                                               |
| plant_short_name_solar       | String       | Short name of the solar plant                                    |
| plant_long_name_solar        | String       | Long name of the solar plant                                     |
| generation_solar             | Float        | Solar plant generation (daily)                                   |
| pr                           | Float        | Performance ratio (solar)                                        |
| poa                          | Float        | Plane of array irradiance (solar)                                |
| generation_solar_monthly     | Float        | Solar plant generation (monthly)                                 |
| pr_monthly                   | Float        | Performance ratio (solar, monthly)                               |
| poa_monthly                  | Float        | Plane of array irradiance (solar, monthly)                       |
| plant_short_name_wind        | String       | Short name of the wind plant                                     |
| plant_long_name_wind         | String       | Long name of the wind plant                                      |
| generation_wind              | Float        | Wind plant generation (daily)                                    |
| wind_speed                   | Float        | Wind speed (daily)                                               |
| generation_wind_monthly      | Float        | Wind plant generation (monthly)                                  |
| wind_speed_monthly           | Float        | Wind speed (monthly)                                             |
| edit_generation_solar        | Float        | Edited solar generation (daily)                                  |
| edit_pr                      | Float        | Edited performance ratio (solar)                                 |
| edit_poa                     | Float        | Edited plane of array irradiance (solar)                         |
| edit_generation_solar_monthly| Float        | Edited solar generation (monthly)                                |
| edit_pr_monthly              | Float        | Edited performance ratio (solar, monthly)                        |
| edit_poa_monthly             | Float        | Edited plane of array irradiance (solar, monthly)                |
| edit_generation_wind         | Float        | Edited wind generation (daily)                                   |
| edit_wind_speed              | Float        | Edited wind speed (daily)                                        |
| edit_generation_wind_monthly | Float        | Edited wind generation (monthly)                                 |
| edit_wind_speed_monthly      | Float        | Edited wind speed (monthly)                                      |
| csv_report_data              | Text         | CSV report data (raw or JSON)                                    |
| edit_csv_report_data         | Text         | Edited CSV report data                                           |
| approved                     | Boolean      | Whether the report is approved                                   |
| review                       | Boolean      | Whether the report is under review                               |
| action_performed             | Boolean      | Whether an action has been performed on the report               |
| status                       | Enum         | Status of the report (Sent, Pending, In Review, etc.)            |
| regenerate                   | Boolean      | Whether the report is marked for regeneration                    |
| dgr_path                     | String(500)  | Path to the DGR report file                                      |
| comments                     | String       | Comments on the report                                           |
| dont_send                    | Boolean      | Whether the report should not be sent                            |
| edit_action                  | Boolean      | Whether an edit action was performed                             |
| save_action                  | Boolean      | Whether a save action was performed                              |
| saved_count                  | Integer      | Number of times the report was saved                             |

---

## Table: `dgr_solar_db` (SolarReport)

| Column Name           | Type         | Description                                      |
|-----------------------|--------------|--------------------------------------------------|
| id                    | Integer (PK) | Unique identifier                                |
| date                  | Date         | Date of the report                               |
| plant_short_name      | String       | Short name of the solar plant                    |
| plant_long_name       | String       | Long name of the solar plant                     |
| generation            | Float        | Solar plant generation (daily)                   |
| pr                    | Float        | Performance ratio (solar)                        |
| poa                   | Float        | Plane of array irradiance (solar)                |
| generation_monthly    | Float        | Solar plant generation (monthly)                 |
| pr_monthly            | Float        | Performance ratio (solar, monthly)               |
| poa_monthly           | Float        | Plane of array irradiance (solar, monthly)       |
| edit_generation       | Float        | Edited solar generation (daily)                  |
| edit_pr               | Float        | Edited performance ratio (solar)                 |
| edit_poa              | Float        | Edited plane of array irradiance (solar)         |
| edit_generation_monthly| Float       | Edited solar generation (monthly)                |
| edit_pr_monthly       | Float        | Edited performance ratio (solar, monthly)        |
| edit_poa_monthly      | Float        | Edited plane of array irradiance (solar, monthly)|
| approved              | Boolean      | Whether the report is approved                   |
| review                | Boolean      | Whether the report is under review               |
| action_performed      | Boolean      | Whether an action has been performed on the report|
| status                | Enum         | Status of the report (Sent, Pending, etc.)       |
| regenerate            | Boolean      | Whether the report is marked for regeneration    |
| dgr_path              | String(500)  | Path to the DGR report file                      |
| comments              | String       | Comments on the report                           |
| dont_send             | Boolean      | Whether the report should not be sent            |
| edit_action           | Boolean      | Whether an edit action was performed             |
| save_action           | Boolean      | Whether a save action was performed              |
| saved_count           | Integer      | Number of times the report was saved             |

---

## Table: `dgr_wind_db` (WindReport)

| Column Name           | Type         | Description                                      |
|-----------------------|--------------|--------------------------------------------------|
| id                    | Integer (PK) | Unique identifier                                |
| date                  | Date         | Date of the report                               |
| plant_short_name      | String       | Short name of the wind plant                     |
| plant_long_name       | String       | Long name of the wind plant                      |
| generation            | Float        | Wind plant generation (daily)                    |
| wind_speed            | Float        | Wind speed (daily)                               |
| generation_monthly    | Float        | Wind plant generation (monthly)                  |
| wind_speed_monthly    | Float        | Wind speed (monthly)                             |
| edit_generation       | Float        | Edited wind generation (daily)                   |
| edit_wind_speed       | Float        | Edited wind speed (daily)                        |
| edit_generation_monthly| Float       | Edited wind generation (monthly)                 |
| edit_wind_speed_monthly| Float       | Edited wind speed (monthly)                      |
| csv_report_data       | Text         | CSV report data (raw or JSON)                    |
| edit_csv_report_data  | Text         | Edited CSV report data                           |
| approved              | Boolean      | Whether the report is approved                   |
| review                | Boolean      | Whether the report is under review               |
| action_performed      | Boolean      | Whether an action has been performed on the report|
| status                | Enum         | Status of the report (Sent, Pending, etc.)       |
| regenerate            | Boolean      | Whether the report is marked for regeneration    |
| dgr_path              | String(500)  | Path to the DGR report file                      |
| comments              | String       | Comments on the report                           |
| dont_send             | Boolean      | Whether the report should not be sent            |
| edit_action           | Boolean      | Whether an edit action was performed             |
| save_action           | Boolean      | Whether a save action was performed              |
| saved_count           | Integer      | Number of times the report was saved             |

---

## Table: `report_status`

| Column Name      | Type         | Description                                      |
|------------------|--------------|--------------------------------------------------|
| id               | Integer (PK) | Unique identifier                                |
| message_id       | String(255)  | Unique message identifier                        |
| recipient_id     | String(255)  | ID of the recipient                              |
| status           | String(50)   | Status of the report/message                     |
| status_updated_at| DateTime     | When the status was last updated                 |
| send_date        | DateTime     | When the report/message was sent                 |
| report_date      | Date         | Date of the report                               |
| plant_id         | String(100)  | Plant identifier                                 |
| client_name      | String(255)  | Name of the client                               |
| type             | String(50)   | Type of report/message                           |
| combined         | String(100)  | Combined field (purpose inferred from name)      |
| contact_person   | Text         | Contact person details                           |

---

**Note:**  
- (PK) denotes Primary Key.  
- Enum fields for `status` include: 'Sent', 'Pending', 'In Review', 'Regenerated', 'Not Sent', 'Sent Updated', 'Saved'.
- Descriptions are inferred from field names and available comments. For more detailed business logic, refer to the application code or documentation.
