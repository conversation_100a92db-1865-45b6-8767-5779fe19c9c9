import os
from pathlib import Path
from typing import Dict, Any, Optional

import pandas as pd

from helper.utils import (
    generate_solar_dgr_pdf,
    get_dynamic_dates,
    fetch_data_total,
    get_capacity_from_csv,
    _safe_mean,
    _safe_sum,
)
from helper.logger_setup import setup_logger
from helper.storage_s3 import upload_file_s3
from DB.db_ops import insert_solar_data_db

# -------------------------------
# Constants
# -------------------------------
STATIC_REPORT_DIR = Path("static") / "solar_final_report"
S3_REPORT_PREFIX = "solar_reports/"

LOGGER = setup_logger("solar_automation", "solar_automation.log")


# -------------------------------
# Data Fetching
# -------------------------------
def fetch_all_solar_data(
    plant_name: str,
    start_date: str,
    current_month_start: str,
    last_30_days_start: str,
    current_year_start: str,
    yearly: str,
    condition_poa: Dict[str, str],
    condition_pr: Dict[str, str],
    condition_generation: Dict[str, str],
    condition_monthly_pr: Dict[str, str]
) -> Dict[str, pd.DataFrame]:
    """
    Fetch all solar data sequentially to reduce RAM usage.
    """
    LOGGER.info(f"[Data Fetch] Plant: {plant_name}")

    return {
        "poa_data": fetch_data_total(plant_name, ["Daily POA Energy"], "Plant", start_date, start_date, condition_poa),
        "pr_data": fetch_data_total(plant_name, ["PR"], "Plant", start_date, start_date, condition_pr),
        "daily_generation": fetch_data_total(plant_name, ["Daily Energy"], "Plant", start_date, start_date, condition_generation),
        "generation_monthly_value": fetch_data_total(plant_name, ["Daily Energy"], "Plant", current_month_start, start_date, condition_generation),
        "pr_data_monthly_value": fetch_data_total(plant_name, ["PR"], "Plant", current_month_start, start_date, condition_monthly_pr),
        "poa_data_monthly_value": fetch_data_total(plant_name, ["Daily POA Energy"], "Plant", current_month_start, start_date, condition_poa),
    }


# -------------------------------
# Main Report Generation
# -------------------------------
def generate_solar_automation_report(
    plant_name: str,
    start_date: str,
    customer_name: str,
    project: str
) -> Optional[Path]:
    """
    Generate and upload a daily solar automation report for a given plant.
    """
    LOGGER.info(f"[Start Report] Plant: {plant_name}, Date: {start_date}, Customer: {customer_name}")

    try:
        # Define fetch conditions
        condition_poa = {"Daily POA Energy": "last"}
        condition_daily_pr = {"PR": "last"}
        condition_monthly_pr = {"PR": "mean"}
        condition_generation = {"Daily Energy": "max"}

        # Get date ranges
        current_month_start, _, _, _, _, last_30_days_start, current_year_start, last_year_date = get_dynamic_dates(start_date)

        # Fetch all required data
        data = fetch_all_solar_data(
            plant_name, start_date, current_month_start, last_30_days_start,
            current_year_start, last_year_date, condition_poa, condition_daily_pr,
            condition_generation, condition_monthly_pr
        )

        # Extract and compute key metrics
        total_generation = _safe_sum(data["daily_generation"])
        month_gen_value = _safe_sum(data["generation_monthly_value"])
        month_pr_value = _safe_mean(data["pr_data_monthly_value"])
        daily_pr_percentage = _safe_mean(data["pr_data"])
        monthly_poa_value = _safe_mean(data["poa_data_monthly_value"])
        avg_poa = _safe_mean(data["poa_data"])

        capacity = get_capacity_from_csv(plant_name)

        # Prepare report path
        STATIC_REPORT_DIR.mkdir(parents=True, exist_ok=True)
        final_pdf_path = STATIC_REPORT_DIR / f"{plant_name}_DGR_{start_date}.jpg"

        # Generate PDF report
        summary_pdf = generate_solar_dgr_pdf(
            start_date, customer_name, avg_poa, daily_pr_percentage,
            total_generation, month_gen_value, month_pr_value,
            monthly_poa_value, capacity, final_pdf_path, comment_text=None
        )

        LOGGER.info(f"[Report Generated] {summary_pdf}")

        # Upload to S3
        s3_path = f"{S3_REPORT_PREFIX}{plant_name}_DGR_{start_date}.jpg"
        upload_file_s3(summary_pdf, s3_path)

        # Insert into DB
        record = {
            "date": start_date,
            "plant_short_name": plant_name,
            "plant_long_name": customer_name,
            "generation": round(total_generation, 2),
            "pr": round(daily_pr_percentage, 2),
            "poa": round(avg_poa, 2),
            "generation_monthly": round(month_gen_value, 2),
            "pr_monthly": round(month_pr_value, 2),
            "poa_monthly": round(monthly_poa_value, 2),
            "approved": 0,
            "review": 0,
            "action_performed": 0,
            "dgr_path": str(summary_pdf)
        }

        insert_solar_data_db([record])
        LOGGER.info(f"[DB Insert] Solar data saved for {plant_name}")

        return summary_pdf

    except Exception as e:
        LOGGER.error(f"[Error] Failed generating report for {plant_name}: {e}", exc_info=True)
        _insert_zero_solar_data(plant_name, start_date, customer_name)
        return None




def _insert_zero_solar_data(plant_name: str, date: str, customer_name: str) -> None:
    """
    Insert a zeroed record into DB when solar report generation fails.
    """
    final_pdf_path = STATIC_REPORT_DIR / f"{plant_name}_DGR_{date}.jpg"
    zero_record = {
        "date": date,
        "plant_short_name": plant_name,
        "plant_long_name": customer_name,
        "generation": 0,
        "pr": 0,
        "poa": 0,
        "generation_monthly": 0,
        "pr_monthly": 0,
        "poa_monthly": 0,
        "approved": 0,
        "review": 0,
        "action_performed": 0,
        "dgr_path": str(final_pdf_path)
    }
    try:
        insert_solar_data_db([zero_record])
        LOGGER.info(f"[Fallback] Zero solar data inserted for {plant_name}")
    except Exception as e:
        LOGGER.error(f"[DB Error] Failed inserting zero solar data: {e}", exc_info=True)
